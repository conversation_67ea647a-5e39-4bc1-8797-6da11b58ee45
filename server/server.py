from contextlib import asynccontextmanager
from collections.abc import <PERSON><PERSON><PERSON><PERSON><PERSON>
from dataclasses import dataclass

from database import MongoDatabase, Database  # Cambiado para usar la nueva implementación

from mcp.server.fastmcp import FastMCP

# Create a named server
mcp = FastMCP("My App")

# Specify dependencies for deployment and development
mcp = FastMCP("My App", dependencies=["pandas", "numpy"])


@dataclass
class AppContext:
    db: Database


@asynccontextmanager
async def app_lifespan(server: FastMCP) -> AsyncIterator[AppContext]:
    """Manage application lifecycle with type-safe context"""
    # Inicializa la base de datos MongoDB (puedes parametrizar el URI y db_name por env vars)
    db = MongoDatabase(uri="mongodb://localhost:27017", db_name="mydb")
    await db.connect()
    try:
        yield AppContext(db=db)
    finally:
        await db.disconnect()


# Pass lifespan to server
mcp = FastMCP("My App", lifespan=app_lifespan)


# Access type-safe lifespan context in tools
@mcp.tool()
def query_db(collection: str = "test", query_filter: dict = None) -> list:
    """Tool que consulta la base de datos MongoDB"""
    ctx = mcp.get_context()
    db = ctx.request_context.lifespan_context["db"]
    query_filter = query_filter or {}
    cursor = db.query(collection, query_filter)
    # MongoDB find devuelve un cursor asíncrono, así que hay que iterar sobre él
    import asyncio

    async def fetch():
        docs = []
        async for doc in cursor:
            docs.append(doc)
            if len(docs) >= 10:
                break
        return docs

    return asyncio.run(fetch())

if __name__ == "__main__":
    mcp.run()