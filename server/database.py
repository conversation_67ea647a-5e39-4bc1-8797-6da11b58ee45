# database.py
# Interfaz y clase concreta para MongoDB usando motor
from abc import ABC, abstractmethod
from typing import Any
from motor.motor_asyncio import AsyncIOMotorClient, AsyncIOMotorDatabase
import os
from dotenv import load_dotenv

class Database(ABC):
    @abstractmethod
    async def connect(self) -> None:
        pass

    @abstractmethod
    async def disconnect(self) -> None:
        pass

    @abstractmethod
    def query(self, collection: str, filter: dict[str, Any]) -> Any:
        pass

class MongoDatabase(Database):
    def __init__(self, uri: str = None, db_name: str = None):
        # Cargar variables de entorno desde .env si existen
        load_dotenv()
        self.uri = uri or os.getenv("MONGO_URI", "mongodb://localhost:27017")
        self.db_name = db_name or os.getenv("MONGO_DB_NAME", "mydb")
        self.client: AsyncIOMotorClient | None = None
        self.db: AsyncIOMotorDatabase | None = None

    async def connect(self) -> None:
        self.client = AsyncIOMotorClient(self.uri)
        self.db = self.client[self.db_name]

    async def disconnect(self) -> None:
        if self.client:
            self.client.close()

    def query(self, collection: str, filter: dict[str, Any]) -> Any:
        if not self.db:
            raise RuntimeError("Database not connected")
        return self.db[collection].find(filter)
