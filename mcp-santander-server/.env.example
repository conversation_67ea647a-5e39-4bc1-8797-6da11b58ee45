# Environment Variables Example
# Copy this file to .env and fill in your values

# Environment
ENVIRONMENT=development

# LLM Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=llama3.2:latest

# Database
MONGODB_URI=mongodb://localhost:27017
MONGODB_DATABASE=mcp_santander

# Cache
REDIS_URL=redis://localhost:6379
REDIS_DB=0

# Monitoring
PROMETHEUS_PORT=8080
LOG_LEVEL=INFO

# API Keys for Internal APIs
INTERNAL_API_KEY=your_internal_api_key
INTERNAL_API_BASE_URL=https://api.internal.company.com
