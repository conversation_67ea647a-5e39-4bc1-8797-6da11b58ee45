"""LLM Client Port - Interface for LLM communication."""

from abc import ABC, abstractmethod
from typing import Any, AsyncIterator, Dict, List, Optional

from ..entities.llm_request import LlmRequest, LlmMessage
from ..value_objects.llm_provider import LlmProvider


class LlmResponse:
    """Response from an LLM request."""
    
    def __init__(
        self,
        content: str,
        model: str,
        provider: LlmProvider,
        usage: Optional[Dict[str, int]] = None,
        metadata: Optional[Dict[str, Any]] = None
    ):
        self.content = content
        self.model = model
        self.provider = provider
        self.usage = usage or {}
        self.metadata = metadata or {}


class LlmClientPort(ABC):
    """
    Port (interface) for LLM client implementations.
    
    This defines the contract that all LLM client adapters must implement.
    Following the hexagonal architecture pattern, this allows the domain
    to be independent of specific LLM provider implementations.
    """
    
    @abstractmethod
    async def generate_completion(self, request: LlmRequest) -> LlmResponse:
        """
        Generate a completion for the given request.
        
        Args:
            request: The LLM request to process
            
        Returns:
            The LLM response
            
        Raises:
            LlmClientError: If the request fails
        """
        pass
    
    @abstractmethod
    async def generate_streaming_completion(
        self, 
        request: LlmRequest
    ) -> AsyncIterator[str]:
        """
        Generate a streaming completion for the given request.
        
        Args:
            request: The LLM request to process
            
        Yields:
            Chunks of the completion text
            
        Raises:
            LlmClientError: If the request fails
        """
        pass
    
    @abstractmethod
    async def generate_chat_completion(
        self,
        messages: List[LlmMessage],
        model: str,
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        **kwargs: Any
    ) -> LlmResponse:
        """
        Generate a chat completion for the given messages.
        
        Args:
            messages: List of conversation messages
            model: Model to use for completion
            temperature: Sampling temperature
            max_tokens: Maximum tokens to generate
            **kwargs: Additional provider-specific parameters
            
        Returns:
            The LLM response
            
        Raises:
            LlmClientError: If the request fails
        """
        pass
    
    @abstractmethod
    async def get_available_models(self) -> List[str]:
        """
        Get list of available models for this provider.
        
        Returns:
            List of model names
            
        Raises:
            LlmClientError: If the request fails
        """
        pass
    
    @abstractmethod
    async def health_check(self) -> bool:
        """
        Check if the LLM client is healthy and can make requests.
        
        Returns:
            True if healthy, False otherwise
        """
        pass
    
    @abstractmethod
    async def get_client_info(self) -> Dict[str, Any]:
        """
        Get information about the LLM client.
        
        Returns:
            Dictionary with client information (provider, models, etc.)
        """
        pass
    
    @abstractmethod
    def get_provider(self) -> LlmProvider:
        """
        Get the LLM provider for this client.
        
        Returns:
            The LLM provider
        """
        pass
