"""API Client Port - Interface for external API communication."""

from abc import ABC, abstractmethod
from typing import Any, AsyncItera<PERSON>, Dict, Optional

from ..entities.api_request import ApiRequest
from ..entities.api_response import ApiResponse


class ApiClientPort(ABC):
    """
    Port (interface) for API client implementations.
    
    This defines the contract that all API client adapters must implement.
    Following the hexagonal architecture pattern, this allows the domain
    to be independent of specific HTTP client implementations.
    """
    
    @abstractmethod
    async def execute_request(self, request: ApiRequest) -> ApiResponse:
        """
        Execute an API request and return the response.
        
        Args:
            request: The API request to execute
            
        Returns:
            The API response
            
        Raises:
            ApiClientError: If the request fails
        """
        pass
    
    @abstractmethod
    async def execute_request_with_retry(
        self, 
        request: ApiRequest,
        max_retries: Optional[int] = None
    ) -> ApiResponse:
        """
        Execute an API request with retry logic.
        
        Args:
            request: The API request to execute
            max_retries: Override the default max retries
            
        Returns:
            The API response
            
        Raises:
            ApiClientError: If the request fails after all retries
        """
        pass
    
    @abstractmethod
    async def execute_batch_requests(
        self, 
        requests: list[ApiRequest]
    ) -> list[ApiResponse]:
        """
        Execute multiple API requests concurrently.
        
        Args:
            requests: List of API requests to execute
            
        Returns:
            List of API responses in the same order as requests
        """
        pass
    
    @abstractmethod
    async def stream_request(
        self, 
        request: ApiRequest
    ) -> AsyncIterator[Dict[str, Any]]:
        """
        Execute a streaming API request.
        
        Args:
            request: The API request to execute
            
        Yields:
            Chunks of response data
            
        Raises:
            ApiClientError: If the request fails
        """
        pass
    
    @abstractmethod
    async def health_check(self) -> bool:
        """
        Check if the API client is healthy and can make requests.
        
        Returns:
            True if healthy, False otherwise
        """
        pass
    
    @abstractmethod
    async def get_client_info(self) -> Dict[str, Any]:
        """
        Get information about the API client.
        
        Returns:
            Dictionary with client information (version, capabilities, etc.)
        """
        pass
