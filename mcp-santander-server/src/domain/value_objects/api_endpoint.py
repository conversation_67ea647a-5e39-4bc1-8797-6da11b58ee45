"""API Endpoint Value Object."""

from dataclasses import dataclass
from typing import Optional
from urllib.parse import urljoin, urlparse


@dataclass(frozen=True)
class ApiEndpoint:
    """
    Value object representing an API endpoint.
    
    Immutable object that encapsulates URL validation and manipulation.
    """
    
    base_url: str
    path: str
    
    def __post_init__(self) -> None:
        """Validate the endpoint after initialization."""
        if not self.base_url:
            raise ValueError("Base URL cannot be empty")
        if not self.path:
            raise ValueError("Path cannot be empty")
        
        # Validate URL format
        parsed = urlparse(self.base_url)
        if not parsed.scheme or not parsed.netloc:
            raise ValueError("Base URL must be a valid URL with scheme and netloc")
        
        # Ensure path starts with /
        if not self.path.startswith('/'):
            object.__setattr__(self, 'path', f'/{self.path}')
    
    @property
    def full_url(self) -> str:
        """Get the complete URL by joining base_url and path."""
        return urljoin(self.base_url.rstrip('/') + '/', self.path.lstrip('/'))
    
    @property
    def domain(self) -> str:
        """Get the domain from the base URL."""
        parsed = urlparse(self.base_url)
        return parsed.netloc
    
    @property
    def scheme(self) -> str:
        """Get the scheme from the base URL."""
        parsed = urlparse(self.base_url)
        return parsed.scheme
    
    def with_path(self, new_path: str) -> 'ApiEndpoint':
        """Create a new endpoint with a different path."""
        return ApiEndpoint(base_url=self.base_url, path=new_path)
    
    def with_base_url(self, new_base_url: str) -> 'ApiEndpoint':
        """Create a new endpoint with a different base URL."""
        return ApiEndpoint(base_url=new_base_url, path=self.path)
    
    def is_secure(self) -> bool:
        """Check if the endpoint uses HTTPS."""
        return self.scheme.lower() == 'https'
    
    def __str__(self) -> str:
        """String representation of the endpoint."""
        return self.full_url
