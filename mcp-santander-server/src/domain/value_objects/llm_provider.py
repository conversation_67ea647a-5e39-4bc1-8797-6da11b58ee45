"""LLM Provider Value Object."""

from enum import Enum


class LlmProvider(Enum):
    """
    Value object representing LLM providers.
    
    Immutable enumeration of supported LLM providers.
    """
    
    OLLAMA = "ollama"
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    AZURE_OPENAI = "azure_openai"
    
    def __str__(self) -> str:
        """String representation of the provider."""
        return self.value
    
    def is_local(self) -> bool:
        """Check if the provider is local."""
        return self == LlmProvider.OLLAMA
    
    def is_remote(self) -> bool:
        """Check if the provider is remote."""
        return not self.is_local()
    
    def requires_api_key(self) -> bool:
        """Check if the provider requires an API key."""
        return self in {
            LlmProvider.OPENAI,
            LlmProvider.ANTHROPIC,
            LlmProvider.AZURE_OPENAI
        }
    
    def supports_streaming(self) -> bool:
        """Check if the provider supports streaming responses."""
        return True  # All current providers support streaming
    
    def get_default_model(self) -> str:
        """Get the default model for this provider."""
        defaults = {
            LlmProvider.OLLAMA: "llama3.2:latest",
            LlmProvider.OPENAI: "gpt-4",
            LlmProvider.ANTHROPIC: "claude-3-sonnet-20240229",
            LlmProvider.AZURE_OPENAI: "gpt-4"
        }
        return defaults[self]
    
    def get_default_base_url(self) -> str:
        """Get the default base URL for this provider."""
        defaults = {
            LlmProvider.OLLAMA: "http://localhost:11434",
            LlmProvider.OPENAI: "https://api.openai.com/v1",
            LlmProvider.ANTHROPIC: "https://api.anthropic.com",
            LlmProvider.AZURE_OPENAI: ""  # Requires custom endpoint
        }
        return defaults[self]
    
    @classmethod
    def from_string(cls, provider_str: str) -> 'LlmProvider':
        """Create an LlmProvider from a string."""
        try:
            return cls(provider_str.lower())
        except ValueError:
            raise ValueError(f"Unsupported LLM provider: {provider_str}")
