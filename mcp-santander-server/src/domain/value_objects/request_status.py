"""Request Status Value Object."""

from enum import Enum


class RequestStatus(Enum):
    """
    Value object representing the status of a request.
    
    Immutable enumeration of possible request states.
    """
    
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    TIMEOUT = "timeout"
    
    def __str__(self) -> str:
        """String representation of the status."""
        return self.value
    
    def is_terminal(self) -> bool:
        """Check if the status is terminal (no further processing)."""
        return self in {
            RequestStatus.COMPLETED,
            RequestStatus.FAILED,
            RequestStatus.CANCELLED,
            RequestStatus.TIMEOUT
        }
    
    def is_successful(self) -> bool:
        """Check if the status indicates success."""
        return self == RequestStatus.COMPLETED
    
    def is_error(self) -> bool:
        """Check if the status indicates an error."""
        return self in {
            RequestStatus.FAILED,
            RequestStatus.TIMEOUT
        }
    
    def can_transition_to(self, new_status: 'RequestStatus') -> bool:
        """Check if transition to new status is valid."""
        valid_transitions = {
            RequestStatus.PENDING: {
                RequestStatus.PROCESSING,
                RequestStatus.CANCELLED
            },
            RequestStatus.PROCESSING: {
                RequestStatus.COMPLETED,
                RequestStatus.FAILED,
                RequestStatus.TIMEOUT,
                RequestStatus.CANCELLED
            },
            # Terminal states cannot transition
            RequestStatus.COMPLETED: set(),
            RequestStatus.FAILED: set(),
            RequestStatus.CANCELLED: set(),
            RequestStatus.TIMEOUT: set(),
        }
        
        return new_status in valid_transitions.get(self, set())
    
    @classmethod
    def from_string(cls, status_str: str) -> 'RequestStatus':
        """Create a RequestStatus from a string."""
        try:
            return cls(status_str.lower())
        except ValueError:
            raise ValueError(f"Invalid request status: {status_str}")
