"""Response Status Value Object."""

from enum import Enum


class ResponseStatus(Enum):
    """
    Value object representing the status of a response.
    
    Immutable enumeration of possible response states.
    """
    
    SUCCESS = "success"
    ERROR = "error"
    TIMEOUT = "timeout"
    NETWORK_ERROR = "network_error"
    PARSE_ERROR = "parse_error"
    
    def __str__(self) -> str:
        """String representation of the status."""
        return self.value
    
    def is_successful(self) -> bool:
        """Check if the status indicates success."""
        return self == ResponseStatus.SUCCESS
    
    def is_error(self) -> bool:
        """Check if the status indicates an error."""
        return self != ResponseStatus.SUCCESS
    
    def is_retryable(self) -> bool:
        """Check if the error is retryable."""
        return self in {
            ResponseStatus.TIMEOUT,
            ResponseStatus.NETWORK_ERROR
        }
    
    @classmethod
    def from_http_status(cls, status_code: int) -> 'ResponseStatus':
        """Create a ResponseStatus from HTTP status code."""
        if 200 <= status_code < 300:
            return cls.SUCCESS
        else:
            return cls.ERROR
    
    @classmethod
    def from_string(cls, status_str: str) -> 'ResponseStatus':
        """Create a ResponseStatus from a string."""
        try:
            return cls(status_str.lower())
        except ValueError:
            raise ValueError(f"Invalid response status: {status_str}")
