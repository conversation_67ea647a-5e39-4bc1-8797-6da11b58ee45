"""Request Method Value Object."""

from enum import Enum


class RequestMethod(Enum):
    """
    Value object representing HTTP request methods.
    
    Immutable enumeration of supported HTTP methods.
    """
    
    GET = "GET"
    POST = "POST"
    PUT = "PUT"
    PATCH = "PATCH"
    DELETE = "DELETE"
    HEAD = "HEAD"
    OPTIONS = "OPTIONS"
    
    def __str__(self) -> str:
        """String representation of the method."""
        return self.value
    
    def is_safe(self) -> bool:
        """Check if the method is considered safe (no side effects)."""
        return self in {RequestMethod.GET, RequestMethod.HEAD, RequestMethod.OPTIONS}
    
    def is_idempotent(self) -> bool:
        """Check if the method is idempotent."""
        return self in {
            RequestMethod.GET,
            RequestMethod.HEAD,
            RequestMethod.PUT,
            RequestMethod.DELETE,
            RequestMethod.OPTIONS
        }
    
    def allows_body(self) -> bool:
        """Check if the method allows a request body."""
        return self in {
            RequestMethod.POST,
            RequestMethod.PUT,
            RequestMethod.PATCH
        }
    
    @classmethod
    def from_string(cls, method_str: str) -> 'RequestMethod':
        """Create a RequestMethod from a string."""
        try:
            return cls(method_str.upper())
        except ValueError:
            raise ValueError(f"Unsupported HTTP method: {method_str}")
