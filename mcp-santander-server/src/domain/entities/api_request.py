"""API Request Entity - Core domain entity representing an API request."""

from dataclasses import dataclass, field
from datetime import datetime
from typing import Any, Dict, Optional
from uuid import UUID, uuid4

from ..value_objects.api_endpoint import ApiEndpoint
from ..value_objects.request_method import Request<PERSON>ethod
from ..value_objects.request_status import RequestStatus


@dataclass
class ApiRequest:
    """
    Core domain entity representing an API request.
    
    This entity encapsulates all the information needed to make
    an API request and track its lifecycle.
    """
    
    id: UUID = field(default_factory=uuid4)
    endpoint: ApiEndpoint = field()
    method: RequestMethod = field()
    headers: Dict[str, str] = field(default_factory=dict)
    query_params: Dict[str, Any] = field(default_factory=dict)
    body: Optional[Dict[str, Any]] = field(default=None)
    timeout: int = field(default=30)
    max_retries: int = field(default=3)
    status: RequestStatus = field(default=RequestStatus.PENDING)
    created_at: datetime = field(default_factory=datetime.utcnow)
    updated_at: datetime = field(default_factory=datetime.utcnow)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self) -> None:
        """Validate the entity after initialization."""
        if self.timeout <= 0:
            raise ValueError("Timeout must be positive")
        if self.max_retries < 0:
            raise ValueError("Max retries cannot be negative")
    
    def mark_as_processing(self) -> None:
        """Mark the request as being processed."""
        self.status = RequestStatus.PROCESSING
        self.updated_at = datetime.utcnow()
    
    def mark_as_completed(self) -> None:
        """Mark the request as completed successfully."""
        self.status = RequestStatus.COMPLETED
        self.updated_at = datetime.utcnow()
    
    def mark_as_failed(self, error_message: str) -> None:
        """Mark the request as failed with an error message."""
        self.status = RequestStatus.FAILED
        self.metadata["error"] = error_message
        self.updated_at = datetime.utcnow()
    
    def add_metadata(self, key: str, value: Any) -> None:
        """Add metadata to the request."""
        self.metadata[key] = value
        self.updated_at = datetime.utcnow()
    
    def is_retryable(self) -> bool:
        """Check if the request can be retried."""
        return (
            self.status == RequestStatus.FAILED and
            self.metadata.get("retry_count", 0) < self.max_retries
        )
    
    def increment_retry_count(self) -> None:
        """Increment the retry count."""
        current_count = self.metadata.get("retry_count", 0)
        self.metadata["retry_count"] = current_count + 1
        self.updated_at = datetime.utcnow()
