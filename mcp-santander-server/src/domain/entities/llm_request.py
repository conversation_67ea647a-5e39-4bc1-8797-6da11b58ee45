"""LLM Request Entity - Core domain entity representing an LLM request."""

from dataclasses import dataclass, field
from datetime import datetime
from typing import Any, Dict, List, Optional
from uuid import UUID, uuid4

from ..value_objects.llm_provider import Llm<PERSON>rovider
from ..value_objects.request_status import RequestStatus


@dataclass
class LlmMessage:
    """Represents a single message in an LLM conversation."""
    role: str  # "system", "user", "assistant"
    content: str
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class LlmRequest:
    """
    Core domain entity representing an LLM request.
    
    This entity encapsulates all the information needed to make
    an LLM request and track its lifecycle.
    """
    
    id: UUID = field(default_factory=uuid4)
    provider: LlmProvider = field()
    model: str = field()
    messages: List[LlmMessage] = field(default_factory=list)
    temperature: float = field(default=0.7)
    max_tokens: Optional[int] = field(default=None)
    stream: bool = field(default=False)
    timeout: int = field(default=60)
    status: RequestStatus = field(default=RequestStatus.PENDING)
    created_at: datetime = field(default_factory=datetime.utcnow)
    updated_at: datetime = field(default_factory=datetime.utcnow)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self) -> None:
        """Validate the entity after initialization."""
        if not 0.0 <= self.temperature <= 2.0:
            raise ValueError("Temperature must be between 0.0 and 2.0")
        if self.max_tokens is not None and self.max_tokens <= 0:
            raise ValueError("Max tokens must be positive")
        if self.timeout <= 0:
            raise ValueError("Timeout must be positive")
        if not self.messages:
            raise ValueError("At least one message is required")
    
    def add_message(self, role: str, content: str, metadata: Optional[Dict[str, Any]] = None) -> None:
        """Add a message to the conversation."""
        if role not in ["system", "user", "assistant"]:
            raise ValueError("Role must be 'system', 'user', or 'assistant'")
        
        message = LlmMessage(
            role=role,
            content=content,
            metadata=metadata or {}
        )
        self.messages.append(message)
        self.updated_at = datetime.utcnow()
    
    def add_system_message(self, content: str) -> None:
        """Add a system message to the conversation."""
        self.add_message("system", content)
    
    def add_user_message(self, content: str) -> None:
        """Add a user message to the conversation."""
        self.add_message("user", content)
    
    def add_assistant_message(self, content: str) -> None:
        """Add an assistant message to the conversation."""
        self.add_message("assistant", content)
    
    def mark_as_processing(self) -> None:
        """Mark the request as being processed."""
        self.status = RequestStatus.PROCESSING
        self.updated_at = datetime.utcnow()
    
    def mark_as_completed(self) -> None:
        """Mark the request as completed successfully."""
        self.status = RequestStatus.COMPLETED
        self.updated_at = datetime.utcnow()
    
    def mark_as_failed(self, error_message: str) -> None:
        """Mark the request as failed with an error message."""
        self.status = RequestStatus.FAILED
        self.metadata["error"] = error_message
        self.updated_at = datetime.utcnow()
    
    def get_total_tokens_estimate(self) -> int:
        """Get an estimate of total tokens in the conversation."""
        # Simple estimation: ~4 characters per token
        total_chars = sum(len(msg.content) for msg in self.messages)
        return total_chars // 4
    
    def get_conversation_context(self) -> str:
        """Get the full conversation as a formatted string."""
        context_parts = []
        for msg in self.messages:
            context_parts.append(f"{msg.role.upper()}: {msg.content}")
        return "\n\n".join(context_parts)
