"""API Response Entity - Core domain entity representing an API response."""

from dataclasses import dataclass, field
from datetime import datetime
from typing import Any, Dict, Optional
from uuid import UUID, uuid4

from ..value_objects.response_status import ResponseStatus


@dataclass
class ApiResponse:
    """
    Core domain entity representing an API response.
    
    This entity encapsulates the response from an API call
    including status, data, and metadata.
    """
    
    id: UUID = field(default_factory=uuid4)
    request_id: UUID = field()
    status_code: int = field()
    status: ResponseStatus = field()
    data: Optional[Dict[str, Any]] = field(default=None)
    headers: Dict[str, str] = field(default_factory=dict)
    error_message: Optional[str] = field(default=None)
    response_time_ms: float = field(default=0.0)
    created_at: datetime = field(default_factory=datetime.utcnow)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self) -> None:
        """Validate the entity after initialization."""
        if self.status_code < 100 or self.status_code > 599:
            raise ValueError("Status code must be between 100 and 599")
        if self.response_time_ms < 0:
            raise ValueError("Response time cannot be negative")
    
    def is_successful(self) -> bool:
        """Check if the response indicates success."""
        return self.status == ResponseStatus.SUCCESS and 200 <= self.status_code < 300
    
    def is_client_error(self) -> bool:
        """Check if the response indicates a client error."""
        return 400 <= self.status_code < 500
    
    def is_server_error(self) -> bool:
        """Check if the response indicates a server error."""
        return 500 <= self.status_code < 600
    
    def is_retryable_error(self) -> bool:
        """Check if the error is retryable (server errors and some client errors)."""
        return (
            self.is_server_error() or
            self.status_code in [408, 429, 502, 503, 504]  # Timeout, Rate limit, Bad gateway, etc.
        )
    
    def add_metadata(self, key: str, value: Any) -> None:
        """Add metadata to the response."""
        self.metadata[key] = value
    
    def get_content_type(self) -> Optional[str]:
        """Get the content type from headers."""
        return self.headers.get("content-type") or self.headers.get("Content-Type")
    
    def get_data_size(self) -> int:
        """Get the approximate size of the response data."""
        if self.data is None:
            return 0
        return len(str(self.data))
