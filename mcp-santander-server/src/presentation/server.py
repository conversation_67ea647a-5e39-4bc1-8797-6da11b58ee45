"""MCP Server implementation with hexagonal architecture."""

import asyncio
import signal
import sys
from typing import Optional

import structlog
from mcp.server.fastmcp import FastMCP

from ..infrastructure.config.container import get_container
from ..infrastructure.config.settings import get_settings
from .mcp.tools import register_api_tools, register_llm_tools
from .mcp.resources import register_api_resources, register_llm_resources, register_system_resources
from .mcp.prompts import register_api_prompts, register_llm_prompts


# Configure structured logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger(__name__)


class McpSantanderServer:
    """
    MCP Santander Server with hexagonal architecture.
    
    This server provides MCP tools, resources, and prompts for
    interacting with internal APIs and LLM services.
    """
    
    def __init__(self):
        self._mcp: Optional[FastMCP] = None
        self._container = None
        self._settings = None
        self._shutdown_event = asyncio.Event()
        self._logger = logger.bind(component="mcp_server")
    
    async def initialize(self) -> None:
        """Initialize the server and its dependencies."""
        try:
            self._logger.info("Initializing MCP Santander Server")
            
            # Load settings
            self._settings = get_settings()
            self._logger.info(
                "Settings loaded",
                environment=self._settings.environment,
                debug=self._settings.server.debug
            )
            
            # Initialize dependency injection container
            self._container = get_container()
            self._logger.info("Dependency injection container initialized")
            
            # Create FastMCP instance
            self._mcp = FastMCP(
                name=self._settings.server.name,
                version=self._settings.server.version
            )
            
            # Register MCP components
            await self._register_components()
            
            # Start monitoring if enabled
            await self._start_monitoring()
            
            self._logger.info("MCP Santander Server initialized successfully")
            
        except Exception as e:
            self._logger.error("Failed to initialize server", error=str(e))
            raise
    
    async def _register_components(self) -> None:
        """Register MCP tools, resources, and prompts."""
        try:
            # Get services from container
            api_service = self._container.api_service()
            llm_service = self._container.llm_service()
            
            self._logger.info("Registering MCP components")
            
            # Register tools
            register_api_tools(self._mcp, api_service)
            register_llm_tools(self._mcp, llm_service)
            self._logger.info("MCP tools registered")
            
            # Register resources
            register_api_resources(self._mcp, api_service)
            register_llm_resources(self._mcp, llm_service)
            register_system_resources(self._mcp)
            self._logger.info("MCP resources registered")
            
            # Register prompts
            register_api_prompts(self._mcp, api_service)
            register_llm_prompts(self._mcp, llm_service)
            self._logger.info("MCP prompts registered")
            
        except Exception as e:
            self._logger.error("Failed to register MCP components", error=str(e))
            raise
    
    async def _start_monitoring(self) -> None:
        """Start monitoring and metrics collection."""
        try:
            if self._settings.monitoring.metrics.get("enabled", False):
                metrics = self._container.metrics()
                metrics.start_server()
                
                # Set system information
                metrics.set_system_info({
                    "name": self._settings.server.name,
                    "version": self._settings.server.version,
                    "environment": self._settings.environment,
                    "python_version": sys.version.split()[0]
                })
                
                self._logger.info(
                    "Monitoring started",
                    port=self._settings.monitoring.metrics.get("port", 8080)
                )
        except Exception as e:
            self._logger.warning("Failed to start monitoring", error=str(e))
    
    async def run(self) -> None:
        """Run the MCP server."""
        if not self._mcp:
            raise RuntimeError("Server not initialized. Call initialize() first.")
        
        try:
            self._logger.info("Starting MCP Santander Server")
            
            # Set up signal handlers for graceful shutdown
            self._setup_signal_handlers()
            
            # Run the MCP server
            await self._mcp.run()
            
        except KeyboardInterrupt:
            self._logger.info("Received keyboard interrupt")
        except Exception as e:
            self._logger.error("Server error", error=str(e))
            raise
        finally:
            await self.shutdown()
    
    def _setup_signal_handlers(self) -> None:
        """Set up signal handlers for graceful shutdown."""
        def signal_handler(signum, frame):
            self._logger.info(f"Received signal {signum}")
            self._shutdown_event.set()
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    async def shutdown(self) -> None:
        """Shutdown the server gracefully."""
        try:
            self._logger.info("Shutting down MCP Santander Server")
            
            # Close container resources
            if self._container:
                # Close cache connections
                try:
                    cache = self._container.cache()
                    if hasattr(cache, 'close'):
                        await cache.close()
                except Exception as e:
                    self._logger.warning("Error closing cache", error=str(e))
                
                # Close API client connections
                try:
                    api_client = self._container.api_client()
                    if hasattr(api_client, '__aexit__'):
                        await api_client.__aexit__(None, None, None)
                except Exception as e:
                    self._logger.warning("Error closing API client", error=str(e))
                
                # Close LLM client connections
                try:
                    llm_client = self._container.llm_client()
                    if hasattr(llm_client, '__aexit__'):
                        await llm_client.__aexit__(None, None, None)
                except Exception as e:
                    self._logger.warning("Error closing LLM client", error=str(e))
            
            self._logger.info("MCP Santander Server shutdown complete")
            
        except Exception as e:
            self._logger.error("Error during shutdown", error=str(e))
    
    async def health_check(self) -> dict:
        """Perform a comprehensive health check."""
        try:
            health_status = {
                "status": "healthy",
                "timestamp": "2024-01-01T00:00:00Z",  # TODO: Use actual timestamp
                "services": {}
            }
            
            if self._container:
                # Check API service
                try:
                    api_service = self._container.api_service()
                    api_health = await api_service.health_check()
                    health_status["services"]["api"] = api_health
                except Exception as e:
                    health_status["services"]["api"] = {"status": "unhealthy", "error": str(e)}
                
                # Check LLM service
                try:
                    llm_service = self._container.llm_service()
                    llm_health = await llm_service.health_check()
                    health_status["services"]["llm"] = llm_health
                except Exception as e:
                    health_status["services"]["llm"] = {"status": "unhealthy", "error": str(e)}
                
                # Check cache
                try:
                    cache = self._container.cache()
                    if hasattr(cache, 'health_check'):
                        cache_healthy = await cache.health_check()
                        health_status["services"]["cache"] = {
                            "status": "healthy" if cache_healthy else "unhealthy"
                        }
                except Exception as e:
                    health_status["services"]["cache"] = {"status": "unhealthy", "error": str(e)}
            
            # Determine overall status
            service_statuses = [
                service.get("status", "unhealthy") 
                for service in health_status["services"].values()
            ]
            
            if any(status == "unhealthy" for status in service_statuses):
                health_status["status"] = "degraded"
            
            return health_status
            
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "timestamp": "2024-01-01T00:00:00Z"
            }


async def create_server() -> McpSantanderServer:
    """
    Create and initialize the MCP server.
    
    Returns:
        Initialized server instance
    """
    server = McpSantanderServer()
    await server.initialize()
    return server


async def main() -> None:
    """Main entry point for the MCP server."""
    try:
        server = await create_server()
        await server.run()
    except Exception as e:
        logger.error("Failed to start server", error=str(e))
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
