"""MCP Resources for exposing data and information."""

from typing import Any, Dict, Optional

import structlog
from mcp.server.fastmcp import FastMCP

from ...application.services.api_service import ApiService
from ...application.services.llm_service import LlmService


logger = structlog.get_logger(__name__)


def register_api_resources(mcp: FastMCP, api_service: ApiService) -> None:
    """Register API-related MCP resources."""
    
    @mcp.resource("api://health")
    async def api_health() -> str:
        """
        Get API service health status.
        
        Returns:
            JSON string with health information
        """
        try:
            health = await api_service.health_check()
            return f"""
# API Service Health

**Status**: {health['status']}
**Repository**: {health['repository']}
**Timestamp**: {health['timestamp']}

## Details
- Service is {'healthy' if health['status'] == 'healthy' else 'experiencing issues'}
- Repository connection: {'OK' if health['repository'] == 'healthy' else 'Failed'}

## Available Operations
- Execute API requests with retry logic
- Analyze API responses with LLM
- Get optimization suggestions
- List and filter API requests
"""
        except Exception as e:
            return f"""
# API Service Health

**Status**: Error
**Error**: {str(e)}

The API service is currently unavailable.
"""
    
    @mcp.resource("api://stats")
    async def api_stats() -> str:
        """
        Get API service statistics and metrics.
        
        Returns:
            Markdown string with API statistics
        """
        try:
            # In a real implementation, you would get actual stats
            # For now, return placeholder information
            return """
# API Service Statistics

## Request Statistics
- **Total Requests**: 1,234
- **Successful Requests**: 1,156 (93.7%)
- **Failed Requests**: 78 (6.3%)
- **Average Response Time**: 245ms

## Popular Endpoints
1. `/api/users` - 456 requests
2. `/api/orders` - 234 requests  
3. `/api/products` - 189 requests

## Error Analysis
- **Timeout Errors**: 23 (29.5%)
- **Network Errors**: 18 (23.1%)
- **Server Errors**: 37 (47.4%)

## Cache Performance
- **Cache Hit Rate**: 78.5%
- **Cache Size**: 2.3MB
- **Cached Entries**: 1,567
"""
        except Exception as e:
            return f"Error retrieving API statistics: {str(e)}"
    
    @mcp.resource("api://endpoints")
    async def api_endpoints() -> str:
        """
        Get information about available API endpoints and their usage.
        
        Returns:
            Markdown string with endpoint information
        """
        return """
# Available API Endpoints

## Internal Services

### User Service
- **Base URL**: `https://api.internal.com/users`
- **Endpoints**:
  - `GET /users` - List all users
  - `GET /users/{id}` - Get user by ID
  - `POST /users` - Create new user
  - `PUT /users/{id}` - Update user
  - `DELETE /users/{id}` - Delete user

### Order Service  
- **Base URL**: `https://api.internal.com/orders`
- **Endpoints**:
  - `GET /orders` - List orders
  - `GET /orders/{id}` - Get order details
  - `POST /orders` - Create new order
  - `PUT /orders/{id}/status` - Update order status

### Product Service
- **Base URL**: `https://api.internal.com/products`
- **Endpoints**:
  - `GET /products` - List products
  - `GET /products/{id}` - Get product details
  - `POST /products` - Create new product
  - `PUT /products/{id}` - Update product

## Authentication
Most endpoints require authentication via Bearer token in the Authorization header:
```
Authorization: Bearer <your-token>
```

## Rate Limits
- **Default**: 1000 requests per hour
- **Burst**: 100 requests per minute
"""


def register_llm_resources(mcp: FastMCP, llm_service: LlmService) -> None:
    """Register LLM-related MCP resources."""
    
    @mcp.resource("llm://health")
    async def llm_health() -> str:
        """
        Get LLM service health status.
        
        Returns:
            JSON string with health information
        """
        try:
            health = await llm_service.health_check()
            return f"""
# LLM Service Health

**Status**: {health['status']}
**Provider**: {health['provider']}
**LLM Client**: {health['llm_client']}
**Repository**: {health['repository']}
**Timestamp**: {health['timestamp']}

## Client Information
{health.get('client_info', {})}

## Available Operations
- Generate text completions
- Analyze API responses
- Provide optimization suggestions
- Support for multiple providers (Ollama, OpenAI)
"""
        except Exception as e:
            return f"""
# LLM Service Health

**Status**: Error
**Error**: {str(e)}

The LLM service is currently unavailable.
"""
    
    @mcp.resource("llm://providers")
    async def llm_providers() -> str:
        """
        Get information about available LLM providers.
        
        Returns:
            Markdown string with provider information
        """
        try:
            # Get info for known providers
            providers_info = []
            
            for provider in ["ollama", "openai"]:
                try:
                    info = await llm_service.get_provider_info(provider)
                    providers_info.append((provider, info))
                except Exception:
                    providers_info.append((provider, {"error": "Not available"}))
            
            content = "# Available LLM Providers\n\n"
            
            for provider, info in providers_info:
                content += f"## {provider.title()}\n"
                
                if "error" in info:
                    content += f"**Status**: {info['error']}\n\n"
                    continue
                
                content += f"**Status**: {'Local' if info.get('is_local') else 'Remote'}\n"
                content += f"**Requires API Key**: {'Yes' if info.get('requires_api_key') else 'No'}\n"
                content += f"**Supports Streaming**: {'Yes' if info.get('supports_streaming') else 'No'}\n"
                content += f"**Default Model**: {info.get('default_model', 'N/A')}\n"
                content += f"**Base URL**: {info.get('default_base_url', 'N/A')}\n\n"
            
            return content
            
        except Exception as e:
            return f"Error retrieving LLM provider information: {str(e)}"
    
    @mcp.resource("llm://models/{provider}")
    async def llm_models(provider: str) -> str:
        """
        Get available models for a specific LLM provider.
        
        Args:
            provider: The LLM provider name
            
        Returns:
            Markdown string with model information
        """
        try:
            models = await llm_service.get_available_models(provider)
            
            content = f"# Available Models for {provider.title()}\n\n"
            
            if not models:
                content += "No models available or provider not configured.\n"
                return content
            
            content += f"**Total Models**: {len(models)}\n\n"
            content += "## Models\n"
            
            for i, model in enumerate(models, 1):
                content += f"{i}. `{model}`\n"
            
            content += "\n## Usage\n"
            content += f"To use a model from {provider}, specify it in your LLM completion request:\n"
            content += "```json\n"
            content += "{\n"
            content += f'  "provider": "{provider}",\n'
            content += f'  "model": "{models[0] if models else 'model-name'}",\n'
            content += '  "messages": [...]\n'
            content += "}\n"
            content += "```\n"
            
            return content
            
        except Exception as e:
            return f"Error retrieving models for {provider}: {str(e)}"


def register_system_resources(mcp: FastMCP) -> None:
    """Register system-related MCP resources."""
    
    @mcp.resource("system://info")
    async def system_info() -> str:
        """
        Get system information and configuration.
        
        Returns:
            Markdown string with system information
        """
        return """
# MCP Santander Server

## System Information
- **Version**: 0.1.0
- **Architecture**: Hexagonal (Ports & Adapters)
- **Environment**: Development
- **Python Version**: 3.13+

## Features
- **Multi-LLM Support**: Local (Ollama) and Remote (OpenAI) providers
- **API Client**: HTTP client with retry logic and circuit breakers
- **Caching**: Memory and Redis cache adapters
- **Monitoring**: Prometheus metrics and structured logging
- **Persistence**: MongoDB support for request/response storage

## Architecture Layers
1. **Domain**: Core business logic and entities
2. **Application**: Use cases and application services  
3. **Infrastructure**: External service adapters
4. **Presentation**: MCP tools, resources, and prompts

## Configuration
The server supports configuration via:
- YAML files (`config/development.yaml`, `config/production.yaml`)
- Environment variables (`.env` file)
- Runtime dependency injection

## Health Checks
- API Service: `api://health`
- LLM Service: `llm://health`
- System Status: Available via monitoring endpoints
"""
    
    @mcp.resource("system://help")
    async def system_help() -> str:
        """
        Get help information for using the MCP server.
        
        Returns:
            Markdown string with usage help
        """
        return """
# MCP Santander Server - Help

## Available Tools

### API Tools
- `execute_api_request` - Execute HTTP requests to internal APIs
- `list_api_requests` - List recent API requests with filtering
- `analyze_api_response` - Analyze API responses using LLM
- `suggest_api_optimization` - Get LLM-powered optimization suggestions

### LLM Tools  
- `generate_llm_completion` - Generate text completions
- `get_available_llm_models` - List available models for a provider
- `get_llm_provider_info` - Get provider information

## Available Resources
- `api://health` - API service health status
- `api://stats` - API usage statistics
- `api://endpoints` - Available API endpoints
- `llm://health` - LLM service health status
- `llm://providers` - Available LLM providers
- `llm://models/{provider}` - Models for specific provider
- `system://info` - System information
- `system://help` - This help information

## Example Usage

### Execute an API Request
```json
{
  "tool": "execute_api_request",
  "arguments": {
    "base_url": "https://api.internal.com",
    "path": "/users/123",
    "method": "GET",
    "headers": {
      "Authorization": "Bearer token"
    }
  }
}
```

### Generate LLM Completion
```json
{
  "tool": "generate_llm_completion", 
  "arguments": {
    "messages": [
      {"role": "user", "content": "Explain API optimization"}
    ],
    "provider": "ollama",
    "model": "llama3.2:latest"
  }
}
```

## Getting Started
1. Configure your environment (see `.env.example`)
2. Start required services (Ollama, Redis, MongoDB if needed)
3. Use the tools to interact with internal APIs
4. Leverage LLM capabilities for analysis and optimization
"""
