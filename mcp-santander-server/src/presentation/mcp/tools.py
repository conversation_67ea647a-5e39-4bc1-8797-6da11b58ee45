"""MCP Tools for API and LLM operations."""

from typing import Any, Dict, List, Optional
from uuid import UUID

import structlog
from mcp.server.fastmcp import FastMC<PERSON>, Context

from ...application.dto.api_request_dto import ExecuteApiRequestDto, ApiRequestListDto
from ...application.dto.llm_request_dto import (
    LlmCompletionDto, LlmMessageDto, AnalyzeApiResponseDto, OptimizationSuggestionDto
)
from ...application.services.api_service import ApiService
from ...application.services.llm_service import LlmService


logger = structlog.get_logger(__name__)


def register_api_tools(mcp: FastMCP, api_service: ApiService) -> None:
    """Register API-related MCP tools."""
    
    @mcp.tool()
    async def execute_api_request(
        base_url: str,
        path: str,
        method: str = "GET",
        headers: Optional[Dict[str, str]] = None,
        query_params: Optional[Dict[str, Any]] = None,
        body: Optional[Dict[str, Any]] = None,
        timeout: int = 30,
        use_cache: bool = True,
        llm_context: Optional[str] = None,
        ctx: Context = None
    ) -> Dict[str, Any]:
        """
        Execute an API request to an internal service.
        
        Args:
            base_url: Base URL of the API (e.g., "https://api.internal.com")
            path: API endpoint path (e.g., "/users/123")
            method: HTTP method (GET, POST, PUT, DELETE, etc.)
            headers: Optional request headers
            query_params: Optional query parameters
            body: Optional request body for POST/PUT requests
            timeout: Request timeout in seconds
            use_cache: Whether to use caching for the request
            llm_context: Optional context for LLM-assisted error handling
            
        Returns:
            Dictionary with request execution results
        """
        try:
            ctx.info(f"Executing API request: {method} {base_url}{path}")
            
            # Create execution DTO
            dto = ExecuteApiRequestDto(
                base_url=base_url,
                path=path,
                method=method,
                headers=headers or {},
                query_params=query_params or {},
                body=body,
                timeout=timeout,
                use_cache=use_cache,
                llm_context=llm_context
            )
            
            # Execute the request
            result = await api_service.execute_api_request(dto)
            
            ctx.info(f"API request completed: {result.status}")
            
            return {
                "success": True,
                "request_id": str(result.id),
                "status": result.status,
                "method": result.method,
                "url": f"{result.base_url}{result.path}",
                "created_at": result.created_at,
                "metadata": result.metadata
            }
            
        except Exception as e:
            ctx.error(f"API request failed: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "error_type": type(e).__name__
            }
    
    @mcp.tool()
    async def list_api_requests(
        limit: int = 10,
        offset: int = 0,
        status_filter: Optional[str] = None,
        method_filter: Optional[str] = None,
        base_url_filter: Optional[str] = None,
        ctx: Context = None
    ) -> Dict[str, Any]:
        """
        List recent API requests with optional filtering.
        
        Args:
            limit: Maximum number of requests to return (1-100)
            offset: Number of requests to skip
            status_filter: Filter by request status (pending, processing, completed, failed)
            method_filter: Filter by HTTP method (GET, POST, etc.)
            base_url_filter: Filter by base URL
            
        Returns:
            Dictionary with list of API requests
        """
        try:
            ctx.info(f"Listing API requests: limit={limit}, offset={offset}")
            
            # Create list DTO
            dto = ApiRequestListDto(
                limit=min(limit, 100),  # Cap at 100
                offset=offset,
                status_filter=status_filter,
                method_filter=method_filter,
                base_url_filter=base_url_filter
            )
            
            # Get the requests
            requests = await api_service.list_api_requests(dto)
            
            ctx.info(f"Found {len(requests)} API requests")
            
            return {
                "success": True,
                "count": len(requests),
                "requests": [
                    {
                        "id": str(req.id),
                        "method": req.method,
                        "url": f"{req.base_url}{req.path}",
                        "status": req.status,
                        "created_at": req.created_at,
                        "updated_at": req.updated_at
                    }
                    for req in requests
                ]
            }
            
        except Exception as e:
            ctx.error(f"Failed to list API requests: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "error_type": type(e).__name__
            }
    
    @mcp.tool()
    async def analyze_api_response(
        response_id: str,
        analysis_prompt: str,
        provider: str = "ollama",
        model: Optional[str] = None,
        ctx: Context = None
    ) -> Dict[str, Any]:
        """
        Analyze an API response using LLM to provide insights and recommendations.
        
        Args:
            response_id: ID of the API response to analyze
            analysis_prompt: Specific analysis request or question
            provider: LLM provider to use (ollama, openai)
            model: Specific model to use (optional)
            
        Returns:
            Dictionary with LLM analysis results
        """
        try:
            ctx.info(f"Analyzing API response {response_id} with {provider}")
            
            # Create analysis DTO
            dto = AnalyzeApiResponseDto(
                response_id=UUID(response_id),
                analysis_prompt=analysis_prompt,
                provider=provider,
                model=model
            )
            
            # Perform analysis
            result = await api_service.analyze_api_response(dto)
            
            ctx.info("API response analysis completed")
            
            return {
                "success": True,
                "analysis": result.content,
                "provider": result.provider,
                "model": result.model,
                "metadata": result.metadata
            }
            
        except Exception as e:
            ctx.error(f"API response analysis failed: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "error_type": type(e).__name__
            }
    
    @mcp.tool()
    async def suggest_api_optimization(
        request_id: str,
        response_id: str,
        provider: str = "ollama",
        model: Optional[str] = None,
        ctx: Context = None
    ) -> Dict[str, Any]:
        """
        Get LLM-powered optimization suggestions for an API request/response pair.
        
        Args:
            request_id: ID of the API request
            response_id: ID of the API response
            provider: LLM provider to use (ollama, openai)
            model: Specific model to use (optional)
            
        Returns:
            Dictionary with optimization suggestions
        """
        try:
            ctx.info(f"Generating optimization suggestions for request {request_id}")
            
            # Create optimization DTO
            dto = OptimizationSuggestionDto(
                request_id=UUID(request_id),
                response_id=UUID(response_id),
                provider=provider,
                model=model
            )
            
            # Get suggestions
            result = await api_service.suggest_api_optimization(dto)
            
            ctx.info("Optimization suggestions generated")
            
            return {
                "success": True,
                "suggestions": result.suggestions,
                "request_id": str(result.request_id),
                "response_id": str(result.response_id),
                "analysis_timestamp": result.analysis_timestamp
            }
            
        except Exception as e:
            ctx.error(f"Optimization suggestion failed: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "error_type": type(e).__name__
            }


def register_llm_tools(mcp: FastMCP, llm_service: LlmService) -> None:
    """Register LLM-related MCP tools."""
    
    @mcp.tool()
    async def generate_llm_completion(
        messages: List[Dict[str, str]],
        provider: str = "ollama",
        model: str = "llama3.2:latest",
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        stream: bool = False,
        ctx: Context = None
    ) -> Dict[str, Any]:
        """
        Generate an LLM completion for the given conversation.
        
        Args:
            messages: List of messages with 'role' and 'content' keys
            provider: LLM provider to use (ollama, openai)
            model: Model name to use
            temperature: Sampling temperature (0.0 to 2.0)
            max_tokens: Maximum tokens to generate
            stream: Whether to stream the response
            
        Returns:
            Dictionary with LLM completion results
        """
        try:
            ctx.info(f"Generating LLM completion with {provider}/{model}")
            
            # Convert messages to DTOs
            message_dtos = [
                LlmMessageDto(
                    role=msg["role"],
                    content=msg["content"],
                    metadata=msg.get("metadata", {})
                )
                for msg in messages
            ]
            
            # Create completion DTO
            dto = LlmCompletionDto(
                provider=provider,
                model=model,
                messages=message_dtos,
                temperature=temperature,
                max_tokens=max_tokens,
                stream=stream
            )
            
            # Generate completion
            result = await llm_service.generate_completion(dto)
            
            ctx.info("LLM completion generated successfully")
            
            return {
                "success": True,
                "content": result.content,
                "model": result.model,
                "provider": result.provider,
                "usage": result.usage,
                "metadata": result.metadata
            }
            
        except Exception as e:
            ctx.error(f"LLM completion failed: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "error_type": type(e).__name__
            }
    
    @mcp.tool()
    async def get_available_llm_models(
        provider: str = "ollama",
        ctx: Context = None
    ) -> Dict[str, Any]:
        """
        Get list of available models for an LLM provider.
        
        Args:
            provider: LLM provider name (ollama, openai)
            
        Returns:
            Dictionary with available models
        """
        try:
            ctx.info(f"Getting available models for {provider}")
            
            models = await llm_service.get_available_models(provider)
            
            ctx.info(f"Found {len(models)} models for {provider}")
            
            return {
                "success": True,
                "provider": provider,
                "models": models,
                "count": len(models)
            }
            
        except Exception as e:
            ctx.error(f"Failed to get models for {provider}: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "error_type": type(e).__name__
            }
    
    @mcp.tool()
    async def get_llm_provider_info(
        provider: str,
        ctx: Context = None
    ) -> Dict[str, Any]:
        """
        Get information about an LLM provider.
        
        Args:
            provider: LLM provider name
            
        Returns:
            Dictionary with provider information
        """
        try:
            ctx.info(f"Getting provider info for {provider}")
            
            info = await llm_service.get_provider_info(provider)
            
            return {
                "success": True,
                "provider": provider,
                "info": info
            }
            
        except Exception as e:
            ctx.error(f"Failed to get provider info: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "error_type": type(e).__name__
            }
