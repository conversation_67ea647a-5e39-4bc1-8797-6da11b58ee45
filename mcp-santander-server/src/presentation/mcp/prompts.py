"""MCP Prompts for guided interactions."""

from typing import Any, Dict, List, Optional

import structlog
from mcp.server.fastmcp import FastMCP

from ...application.services.api_service import ApiService
from ...application.services.llm_service import LlmService


logger = structlog.get_logger(__name__)


def register_api_prompts(mcp: FastMCP, api_service: ApiService) -> None:
    """Register API-related MCP prompts."""
    
    @mcp.prompt("api-request-builder")
    async def api_request_builder(
        service_name: Optional[str] = None,
        operation: Optional[str] = None,
        **kwargs
    ) -> str:
        """
        Help build an API request with guided prompts.
        
        Args:
            service_name: Name of the service to call
            operation: Type of operation (list, get, create, update, delete)
            
        Returns:
            Guided prompt for building API requests
        """
        base_prompt = """
# API Request Builder

I'll help you build an API request to internal services. Let me guide you through the process.

## Step 1: Service Selection
"""
        
        if not service_name:
            base_prompt += """
Which service would you like to call?
- **users** - User management service
- **orders** - Order processing service  
- **products** - Product catalog service
- **payments** - Payment processing service
- **notifications** - Notification service

Please specify the service name.
"""
            return base_prompt
        
        base_prompt += f"**Selected Service**: {service_name}\n\n"
        
        if not operation:
            base_prompt += """
## Step 2: Operation Type
What operation would you like to perform?
- **list** - Get a list of items
- **get** - Get a specific item by ID
- **create** - Create a new item
- **update** - Update an existing item
- **delete** - Delete an item

Please specify the operation type.
"""
            return base_prompt
        
        # Generate specific guidance based on service and operation
        service_configs = {
            "users": {
                "base_url": "https://api.internal.com/users",
                "operations": {
                    "list": {"method": "GET", "path": "/users", "params": ["limit", "offset", "filter"]},
                    "get": {"method": "GET", "path": "/users/{id}", "params": ["id"]},
                    "create": {"method": "POST", "path": "/users", "body": ["name", "email", "role"]},
                    "update": {"method": "PUT", "path": "/users/{id}", "body": ["name", "email", "role"]},
                    "delete": {"method": "DELETE", "path": "/users/{id}", "params": ["id"]}
                }
            },
            "orders": {
                "base_url": "https://api.internal.com/orders",
                "operations": {
                    "list": {"method": "GET", "path": "/orders", "params": ["status", "user_id", "limit"]},
                    "get": {"method": "GET", "path": "/orders/{id}", "params": ["id"]},
                    "create": {"method": "POST", "path": "/orders", "body": ["user_id", "items", "total"]},
                    "update": {"method": "PUT", "path": "/orders/{id}/status", "body": ["status"]},
                    "delete": {"method": "DELETE", "path": "/orders/{id}", "params": ["id"]}
                }
            }
        }
        
        config = service_configs.get(service_name, {})
        op_config = config.get("operations", {}).get(operation, {})
        
        if not op_config:
            return f"""
## Error
Service '{service_name}' or operation '{operation}' not found.
Please check the available services and operations.
"""
        
        base_prompt += f"""
## Step 3: Request Configuration
**Operation**: {operation}
**Method**: {op_config['method']}
**Base URL**: {config['base_url']}
**Path**: {op_config['path']}

### Required Parameters:
"""
        
        if "params" in op_config:
            for param in op_config["params"]:
                base_prompt += f"- **{param}**: [Specify value]\n"
        
        if "body" in op_config:
            base_prompt += "\n### Request Body Fields:\n"
            for field in op_config["body"]:
                base_prompt += f"- **{field}**: [Specify value]\n"
        
        base_prompt += f"""

### Example Request:
```json
{{
  "base_url": "{config['base_url']}",
  "path": "{op_config['path']}",
  "method": "{op_config['method']}",
  "headers": {{
    "Authorization": "Bearer YOUR_TOKEN",
    "Content-Type": "application/json"
  }}
"""
        
        if "body" in op_config:
            base_prompt += ',\n  "body": {\n'
            for i, field in enumerate(op_config["body"]):
                comma = "," if i < len(op_config["body"]) - 1 else ""
                base_prompt += f'    "{field}": "value"{comma}\n'
            base_prompt += "  }\n"
        
        base_prompt += "}\n```\n"
        
        return base_prompt
    
    @mcp.prompt("api-error-analyzer")
    async def api_error_analyzer(
        status_code: Optional[int] = None,
        error_message: Optional[str] = None,
        **kwargs
    ) -> str:
        """
        Analyze API errors and provide troubleshooting guidance.
        
        Args:
            status_code: HTTP status code of the error
            error_message: Error message received
            
        Returns:
            Error analysis and troubleshooting guide
        """
        base_prompt = """
# API Error Analyzer

I'll help you analyze and troubleshoot API errors.

"""
        
        if not status_code:
            return base_prompt + """
## Error Information Needed
Please provide the following information:
- **Status Code**: HTTP status code (e.g., 404, 500)
- **Error Message**: Any error message received
- **Request Details**: Method, URL, headers, body

This will help me provide specific troubleshooting guidance.
"""
        
        error_guides = {
            400: {
                "title": "Bad Request",
                "description": "The request was invalid or malformed",
                "common_causes": [
                    "Missing required parameters",
                    "Invalid parameter values",
                    "Malformed JSON in request body",
                    "Invalid data types"
                ],
                "solutions": [
                    "Validate all required parameters are included",
                    "Check parameter value formats and types",
                    "Verify JSON syntax in request body",
                    "Review API documentation for parameter requirements"
                ]
            },
            401: {
                "title": "Unauthorized",
                "description": "Authentication failed or missing",
                "common_causes": [
                    "Missing Authorization header",
                    "Invalid or expired token",
                    "Incorrect authentication method"
                ],
                "solutions": [
                    "Include valid Authorization header",
                    "Refresh expired tokens",
                    "Verify authentication method (Bearer, Basic, etc.)",
                    "Check token permissions and scopes"
                ]
            },
            403: {
                "title": "Forbidden",
                "description": "Access denied - insufficient permissions",
                "common_causes": [
                    "Insufficient user permissions",
                    "Resource access restrictions",
                    "Rate limiting"
                ],
                "solutions": [
                    "Verify user has required permissions",
                    "Check resource access policies",
                    "Review rate limiting policies",
                    "Contact administrator for access"
                ]
            },
            404: {
                "title": "Not Found",
                "description": "Resource not found",
                "common_causes": [
                    "Incorrect URL or endpoint",
                    "Resource doesn't exist",
                    "Typo in resource ID"
                ],
                "solutions": [
                    "Verify the URL and endpoint are correct",
                    "Check if the resource ID exists",
                    "Review API documentation for correct paths",
                    "Ensure the resource hasn't been deleted"
                ]
            },
            500: {
                "title": "Internal Server Error",
                "description": "Server-side error occurred",
                "common_causes": [
                    "Server application error",
                    "Database connection issues",
                    "Unhandled exceptions"
                ],
                "solutions": [
                    "Retry the request after a delay",
                    "Check server logs for details",
                    "Contact API support team",
                    "Verify server health status"
                ]
            }
        }
        
        guide = error_guides.get(status_code, {
            "title": f"HTTP {status_code}",
            "description": "Unknown error occurred",
            "common_causes": ["Unexpected error condition"],
            "solutions": ["Check API documentation", "Contact support"]
        })
        
        result = f"""
## Error Analysis: {guide['title']} ({status_code})

**Description**: {guide['description']}
"""
        
        if error_message:
            result += f"\n**Error Message**: {error_message}\n"
        
        result += f"""
### Common Causes:
"""
        for cause in guide["common_causes"]:
            result += f"- {cause}\n"
        
        result += f"""
### Recommended Solutions:
"""
        for solution in guide["solutions"]:
            result += f"- {solution}\n"
        
        result += """
### Next Steps:
1. Apply the recommended solutions above
2. Test the request again
3. If the error persists, use the `analyze_api_response` tool for detailed analysis
4. Consider using LLM assistance for complex troubleshooting
"""
        
        return result


def register_llm_prompts(mcp: FastMCP, llm_service: LlmService) -> None:
    """Register LLM-related MCP prompts."""
    
    @mcp.prompt("llm-conversation-starter")
    async def llm_conversation_starter(
        topic: Optional[str] = None,
        provider: str = "ollama",
        **kwargs
    ) -> str:
        """
        Start a conversation with an LLM on a specific topic.
        
        Args:
            topic: The topic to discuss
            provider: LLM provider to use
            
        Returns:
            Conversation starter prompt
        """
        if not topic:
            return """
# LLM Conversation Starter

What would you like to discuss? I can help with:

## Technical Topics
- **API Design** - Best practices, patterns, optimization
- **System Architecture** - Microservices, scalability, patterns
- **Data Analysis** - Interpreting API responses, metrics
- **Troubleshooting** - Error analysis, debugging strategies

## Business Topics  
- **Process Optimization** - Workflow improvements
- **Integration Strategies** - System integration approaches
- **Performance Analysis** - Bottleneck identification

Please specify a topic to get started.
"""
        
        topic_prompts = {
            "api-design": """
# API Design Discussion

Let's discuss API design best practices and patterns. I can help with:

- **RESTful Design**: Resource modeling, HTTP methods, status codes
- **API Versioning**: Strategies for evolving APIs
- **Error Handling**: Consistent error responses and codes
- **Documentation**: OpenAPI/Swagger specifications
- **Security**: Authentication, authorization, rate limiting
- **Performance**: Caching, pagination, optimization

What specific aspect of API design would you like to explore?
""",
            "troubleshooting": """
# API Troubleshooting Session

I'm here to help troubleshoot API issues. I can assist with:

- **Error Analysis**: Understanding error codes and messages
- **Performance Issues**: Slow responses, timeouts
- **Integration Problems**: Service communication failures
- **Data Issues**: Malformed requests/responses
- **Authentication**: Token and permission problems

What issue are you experiencing? Please provide:
- Error details (status codes, messages)
- Request information (method, URL, headers)
- Expected vs actual behavior
""",
            "optimization": """
# API Optimization Consultation

Let's optimize your API performance and efficiency. Areas we can explore:

- **Response Time**: Reducing latency and improving speed
- **Caching Strategies**: When and how to cache responses
- **Request Optimization**: Minimizing payload size and calls
- **Error Reduction**: Improving reliability and success rates
- **Resource Usage**: Efficient use of system resources

What performance challenges are you facing?
"""
        }
        
        return topic_prompts.get(topic, f"""
# Discussion: {topic.title()}

I'm ready to discuss {topic} with you using the {provider} LLM provider.

Please share your specific questions, challenges, or areas you'd like to explore related to {topic}.

I can provide:
- Detailed explanations and analysis
- Best practices and recommendations  
- Code examples and implementation guidance
- Troubleshooting assistance
- Strategic advice and planning

What would you like to know about {topic}?
""")
    
    @mcp.prompt("code-review-assistant")
    async def code_review_assistant(
        language: Optional[str] = None,
        focus_area: Optional[str] = None,
        **kwargs
    ) -> str:
        """
        Assist with code review and analysis.
        
        Args:
            language: Programming language
            focus_area: Specific area to focus on
            
        Returns:
            Code review assistance prompt
        """
        base_prompt = """
# Code Review Assistant

I'll help you review and analyze code for quality, performance, and best practices.

"""
        
        if not language:
            return base_prompt + """
## Supported Languages
- **Python** - API clients, data processing, web services
- **JavaScript/TypeScript** - Frontend integration, Node.js services
- **Java** - Enterprise applications, microservices
- **Go** - High-performance services, APIs
- **SQL** - Database queries, optimization

Please specify the programming language for targeted review assistance.
"""
        
        focus_areas = {
            "performance": "Performance optimization and efficiency",
            "security": "Security vulnerabilities and best practices", 
            "maintainability": "Code organization and maintainability",
            "testing": "Test coverage and quality",
            "documentation": "Code documentation and comments"
        }
        
        result = base_prompt + f"**Language**: {language.title()}\n"
        
        if focus_area and focus_area in focus_areas:
            result += f"**Focus Area**: {focus_areas[focus_area]}\n"
        
        result += """
## Review Checklist
I'll analyze your code for:

### Code Quality
- Readability and clarity
- Naming conventions
- Code organization
- Design patterns usage

### Performance  
- Algorithm efficiency
- Resource usage
- Potential bottlenecks
- Optimization opportunities

### Security
- Input validation
- Error handling
- Security vulnerabilities
- Best practices compliance

### Maintainability
- Code structure
- Documentation quality
- Test coverage
- Dependency management

Please share the code you'd like me to review, and I'll provide detailed feedback and suggestions.
"""
        
        return result
