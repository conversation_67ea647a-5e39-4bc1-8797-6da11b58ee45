"""LLM Application Service."""

from typing import AsyncIterator, List

from ..dto.llm_request_dto import (
    CreateLlmRequestDto,
    LlmCompletionDto,
    LlmRequestResponseDto,
    LlmResponseDto
)
from ..use_cases.generate_llm_completion import GenerateLlmCompletionUseCase
from ...domain.entities.llm_request import LlmRequest, LlmMessage
from ...domain.value_objects.llm_provider import LlmProvider
from ...domain.ports.llm_client_port import LlmClientPort
from ...domain.ports.repository_port import RepositoryPort


class LlmService:
    """
    Application service for LLM-related operations.
    
    This service coordinates between use cases and provides
    a unified interface for LLM operations.
    """
    
    def __init__(
        self,
        generate_llm_completion_use_case: GenerateLlmCompletionUseCase,
        llm_client: LlmClientPort,
        repository: RepositoryPort
    ):
        self._generate_llm_completion_use_case = generate_llm_completion_use_case
        self._llm_client = llm_client
        self._repository = repository
    
    async def create_llm_request(self, dto: CreateLlmRequestDto) -> LlmRequestResponseDto:
        """
        Create a new LLM request.
        
        Args:
            dto: The creation request DTO
            
        Returns:
            The created LLM request DTO
            
        Raises:
            ValueError: If the request is invalid
            RuntimeError: If the creation fails
        """
        try:
            # Create LLM request entity
            provider = LlmProvider.from_string(dto.provider)
            
            # Convert DTO messages to domain messages
            messages = [
                LlmMessage(
                    role=msg.role,
                    content=msg.content,
                    metadata=msg.metadata
                )
                for msg in dto.messages
            ]
            
            llm_request = LlmRequest(
                provider=provider,
                model=dto.model,
                messages=messages,
                temperature=dto.temperature,
                max_tokens=dto.max_tokens,
                stream=dto.stream,
                timeout=dto.timeout,
                metadata=dto.metadata
            )
            
            # Save to repository
            success = await self._repository.save_llm_request(llm_request)
            if not success:
                raise RuntimeError("Failed to save LLM request")
            
            # Convert to response DTO
            return LlmRequestResponseDto(
                id=llm_request.id,
                provider=str(llm_request.provider),
                model=llm_request.model,
                status=str(llm_request.status),
                created_at=llm_request.created_at.isoformat(),
                updated_at=llm_request.updated_at.isoformat(),
                message_count=len(llm_request.messages),
                estimated_tokens=llm_request.get_total_tokens_estimate(),
                metadata=llm_request.metadata
            )
            
        except Exception as e:
            raise RuntimeError(f"Failed to create LLM request: {str(e)}") from e
    
    async def generate_completion(self, dto: LlmCompletionDto) -> LlmResponseDto:
        """
        Generate an LLM completion.
        
        Args:
            dto: The completion request DTO
            
        Returns:
            The LLM response DTO
        """
        return await self._generate_llm_completion_use_case.execute(dto)
    
    async def generate_streaming_completion(
        self, 
        dto: LlmCompletionDto
    ) -> AsyncIterator[str]:
        """
        Generate a streaming LLM completion.
        
        Args:
            dto: The completion request DTO
            
        Yields:
            Chunks of the completion text
        """
        async for chunk in self._generate_llm_completion_use_case.execute_streaming(dto):
            yield chunk
    
    async def get_available_models(self, provider: str) -> List[str]:
        """
        Get available models for a provider.
        
        Args:
            provider: The LLM provider name
            
        Returns:
            List of available model names
            
        Raises:
            ValueError: If the provider is invalid
        """
        try:
            # Validate provider
            provider_enum = LlmProvider.from_string(provider)
            
            # Check if the current client supports this provider
            if self._llm_client.get_provider() == provider_enum:
                return await self._llm_client.get_available_models()
            else:
                # For now, return default models
                # In a real implementation, you might have multiple clients
                return [provider_enum.get_default_model()]
                
        except Exception as e:
            raise ValueError(f"Failed to get models for provider {provider}: {str(e)}") from e
    
    async def get_provider_info(self, provider: str) -> dict:
        """
        Get information about an LLM provider.
        
        Args:
            provider: The LLM provider name
            
        Returns:
            Provider information dictionary
            
        Raises:
            ValueError: If the provider is invalid
        """
        try:
            provider_enum = LlmProvider.from_string(provider)
            
            return {
                "name": str(provider_enum),
                "is_local": provider_enum.is_local(),
                "is_remote": provider_enum.is_remote(),
                "requires_api_key": provider_enum.requires_api_key(),
                "supports_streaming": provider_enum.supports_streaming(),
                "default_model": provider_enum.get_default_model(),
                "default_base_url": provider_enum.get_default_base_url()
            }
            
        except Exception as e:
            raise ValueError(f"Failed to get info for provider {provider}: {str(e)}") from e
    
    async def health_check(self) -> dict:
        """
        Perform health check for the LLM service.
        
        Returns:
            Health check result
        """
        try:
            # Check LLM client health
            llm_healthy = await self._llm_client.health_check()
            
            # Check repository health
            repo_healthy = await self._repository.health_check()
            
            # Get client info
            client_info = await self._llm_client.get_client_info()
            
            return {
                "status": "healthy" if (llm_healthy and repo_healthy) else "unhealthy",
                "llm_client": "healthy" if llm_healthy else "unhealthy",
                "repository": "healthy" if repo_healthy else "unhealthy",
                "provider": str(self._llm_client.get_provider()),
                "client_info": client_info,
                "timestamp": "2024-01-01T00:00:00Z"  # TODO: Use actual timestamp
            }
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "timestamp": "2024-01-01T00:00:00Z"  # TODO: Use actual timestamp
            }
