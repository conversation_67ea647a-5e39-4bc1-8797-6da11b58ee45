"""API Application Service."""

from typing import List, Optional
from uuid import UUID

from ..dto.api_request_dto import (
    CreateApiRequestDto,
    ExecuteApiRequestDto,
    ApiRequestResponseDto,
    ApiRequestListDto
)
from ..dto.llm_request_dto import (
    AnalyzeApiResponseDto,
    OptimizationSuggestionDto,
    LlmResponseDto,
    OptimizationResultDto
)
from ..use_cases.execute_api_request import ExecuteApiRequestUseCase
from ..use_cases.analyze_api_response import AnalyzeApiResponseUseCase
from ..use_cases.suggest_api_optimization import SuggestApiOptimizationUseCase
from ...domain.entities.api_request import ApiRequest
from ...domain.value_objects.api_endpoint import ApiEndpoint
from ...domain.value_objects.request_method import RequestMethod
from ...domain.ports.repository_port import RepositoryPort


class ApiService:
    """
    Application service for API-related operations.
    
    This service coordinates between use cases and provides
    a unified interface for API operations.
    """
    
    def __init__(
        self,
        execute_api_request_use_case: ExecuteApiRequestUseCase,
        analyze_api_response_use_case: AnalyzeApiResponseUseCase,
        suggest_api_optimization_use_case: SuggestApiOptimizationUseCase,
        repository: RepositoryPort
    ):
        self._execute_api_request_use_case = execute_api_request_use_case
        self._analyze_api_response_use_case = analyze_api_response_use_case
        self._suggest_api_optimization_use_case = suggest_api_optimization_use_case
        self._repository = repository
    
    async def create_api_request(self, dto: CreateApiRequestDto) -> ApiRequestResponseDto:
        """
        Create a new API request.
        
        Args:
            dto: The creation request DTO
            
        Returns:
            The created API request DTO
            
        Raises:
            ValueError: If the request is invalid
            RuntimeError: If the creation fails
        """
        try:
            # Create API request entity
            endpoint = ApiEndpoint(
                base_url=dto.base_url,
                path=dto.path
            )
            
            method = RequestMethod.from_string(dto.method)
            
            api_request = ApiRequest(
                endpoint=endpoint,
                method=method,
                headers=dto.headers,
                query_params=dto.query_params,
                body=dto.body,
                timeout=dto.timeout,
                max_retries=dto.max_retries,
                metadata=dto.metadata
            )
            
            # Save to repository
            success = await self._repository.save_api_request(api_request)
            if not success:
                raise RuntimeError("Failed to save API request")
            
            # Convert to response DTO
            return ApiRequestResponseDto(
                id=api_request.id,
                base_url=api_request.endpoint.base_url,
                path=api_request.endpoint.path,
                method=str(api_request.method),
                status=str(api_request.status),
                created_at=api_request.created_at.isoformat(),
                updated_at=api_request.updated_at.isoformat(),
                metadata=api_request.metadata
            )
            
        except Exception as e:
            raise RuntimeError(f"Failed to create API request: {str(e)}") from e
    
    async def execute_api_request(self, dto: ExecuteApiRequestDto) -> ApiRequestResponseDto:
        """
        Execute an API request.
        
        Args:
            dto: The execution request DTO
            
        Returns:
            The execution result DTO
        """
        return await self._execute_api_request_use_case.execute(dto)
    
    async def get_api_request(self, request_id: UUID) -> Optional[ApiRequestResponseDto]:
        """
        Get an API request by ID.
        
        Args:
            request_id: The request ID
            
        Returns:
            The API request DTO or None if not found
        """
        api_request = await self._repository.get_api_request(request_id)
        if not api_request:
            return None
        
        return ApiRequestResponseDto(
            id=api_request.id,
            base_url=api_request.endpoint.base_url,
            path=api_request.endpoint.path,
            method=str(api_request.method),
            status=str(api_request.status),
            created_at=api_request.created_at.isoformat(),
            updated_at=api_request.updated_at.isoformat(),
            metadata=api_request.metadata
        )
    
    async def list_api_requests(self, dto: ApiRequestListDto) -> List[ApiRequestResponseDto]:
        """
        List API requests with filtering.
        
        Args:
            dto: The list request DTO
            
        Returns:
            List of API request DTOs
        """
        # Build filters
        filters = {}
        if dto.status_filter:
            filters["status"] = dto.status_filter
        if dto.method_filter:
            filters["method"] = dto.method_filter
        if dto.base_url_filter:
            filters["base_url"] = dto.base_url_filter
        
        # Get requests from repository
        api_requests = await self._repository.find_api_requests(
            filters=filters,
            limit=dto.limit,
            offset=dto.offset
        )
        
        # Convert to DTOs
        return [
            ApiRequestResponseDto(
                id=request.id,
                base_url=request.endpoint.base_url,
                path=request.endpoint.path,
                method=str(request.method),
                status=str(request.status),
                created_at=request.created_at.isoformat(),
                updated_at=request.updated_at.isoformat(),
                metadata=request.metadata
            )
            for request in api_requests
        ]
    
    async def analyze_api_response(self, dto: AnalyzeApiResponseDto) -> LlmResponseDto:
        """
        Analyze an API response using LLM.
        
        Args:
            dto: The analysis request DTO
            
        Returns:
            The LLM analysis response DTO
        """
        return await self._analyze_api_response_use_case.execute(dto)
    
    async def suggest_api_optimization(
        self, 
        dto: OptimizationSuggestionDto
    ) -> OptimizationResultDto:
        """
        Suggest API optimizations using LLM.
        
        Args:
            dto: The optimization request DTO
            
        Returns:
            The optimization suggestions DTO
        """
        return await self._suggest_api_optimization_use_case.execute(dto)
    
    async def health_check(self) -> dict:
        """
        Perform health check for the API service.
        
        Returns:
            Health check result
        """
        try:
            # Check repository health
            repo_healthy = await self._repository.health_check()
            
            return {
                "status": "healthy" if repo_healthy else "unhealthy",
                "repository": "healthy" if repo_healthy else "unhealthy",
                "timestamp": "2024-01-01T00:00:00Z"  # TODO: Use actual timestamp
            }
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "timestamp": "2024-01-01T00:00:00Z"  # TODO: Use actual timestamp
            }
