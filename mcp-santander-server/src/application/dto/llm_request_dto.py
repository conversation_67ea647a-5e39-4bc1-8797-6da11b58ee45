"""LLM Request DTOs for application layer."""

from dataclasses import dataclass
from typing import Any, Dict, List, Optional
from uuid import UUID

from pydantic import BaseModel, Field, validator


class LlmMessageDto(BaseModel):
    """DTO for LLM message."""
    
    role: str = Field(..., description="Message role (system, user, assistant)")
    content: str = Field(..., description="Message content")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Message metadata")
    
    @validator('role')
    def validate_role(cls, v):
        """Validate message role."""
        valid_roles = ['system', 'user', 'assistant']
        if v.lower() not in valid_roles:
            raise ValueError(f"Invalid role: {v}")
        return v.lower()


class CreateLlmRequestDto(BaseModel):
    """DTO for creating an LLM request."""
    
    provider: str = Field(..., description="LLM provider")
    model: str = Field(..., description="Model name")
    messages: List[LlmMessageDto] = Field(..., description="Conversation messages")
    temperature: float = Field(default=0.7, description="Sampling temperature")
    max_tokens: Optional[int] = Field(default=None, description="Maximum tokens to generate")
    stream: bool = Field(default=False, description="Whether to stream response")
    timeout: int = Field(default=60, description="Request timeout in seconds")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")
    
    @validator('provider')
    def validate_provider(cls, v):
        """Validate LLM provider."""
        valid_providers = ['ollama', 'openai', 'anthropic', 'azure_openai']
        if v.lower() not in valid_providers:
            raise ValueError(f"Invalid provider: {v}")
        return v.lower()
    
    @validator('temperature')
    def validate_temperature(cls, v):
        """Validate temperature value."""
        if not 0.0 <= v <= 2.0:
            raise ValueError("Temperature must be between 0.0 and 2.0")
        return v
    
    @validator('max_tokens')
    def validate_max_tokens(cls, v):
        """Validate max tokens value."""
        if v is not None and v <= 0:
            raise ValueError("Max tokens must be positive")
        return v
    
    @validator('timeout')
    def validate_timeout(cls, v):
        """Validate timeout value."""
        if v <= 0:
            raise ValueError("Timeout must be positive")
        return v
    
    @validator('messages')
    def validate_messages(cls, v):
        """Validate messages list."""
        if not v:
            raise ValueError("At least one message is required")
        return v


class LlmRequestResponseDto(BaseModel):
    """DTO for LLM request response."""
    
    id: UUID = Field(..., description="Request ID")
    provider: str = Field(..., description="LLM provider")
    model: str = Field(..., description="Model name")
    status: str = Field(..., description="Request status")
    created_at: str = Field(..., description="Creation timestamp")
    updated_at: str = Field(..., description="Last update timestamp")
    message_count: int = Field(..., description="Number of messages")
    estimated_tokens: int = Field(..., description="Estimated token count")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")
    
    class Config:
        """Pydantic configuration."""
        json_encoders = {
            UUID: str
        }


class LlmCompletionDto(BaseModel):
    """DTO for LLM completion request."""
    
    provider: str = Field(..., description="LLM provider")
    model: str = Field(..., description="Model name")
    messages: List[LlmMessageDto] = Field(..., description="Conversation messages")
    temperature: float = Field(default=0.7, description="Sampling temperature")
    max_tokens: Optional[int] = Field(default=None, description="Maximum tokens to generate")
    stream: bool = Field(default=False, description="Whether to stream response")
    
    @validator('provider')
    def validate_provider(cls, v):
        """Validate LLM provider."""
        valid_providers = ['ollama', 'openai', 'anthropic', 'azure_openai']
        if v.lower() not in valid_providers:
            raise ValueError(f"Invalid provider: {v}")
        return v.lower()


class LlmResponseDto(BaseModel):
    """DTO for LLM response."""
    
    content: str = Field(..., description="Generated content")
    model: str = Field(..., description="Model used")
    provider: str = Field(..., description="Provider used")
    usage: Dict[str, int] = Field(default_factory=dict, description="Token usage statistics")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Response metadata")


class AnalyzeApiResponseDto(BaseModel):
    """DTO for analyzing API response with LLM."""
    
    response_id: UUID = Field(..., description="API response ID")
    analysis_prompt: str = Field(..., description="Analysis prompt")
    provider: str = Field(default="ollama", description="LLM provider to use")
    model: Optional[str] = Field(default=None, description="Specific model to use")
    
    @validator('provider')
    def validate_provider(cls, v):
        """Validate LLM provider."""
        valid_providers = ['ollama', 'openai', 'anthropic', 'azure_openai']
        if v.lower() not in valid_providers:
            raise ValueError(f"Invalid provider: {v}")
        return v.lower()


class OptimizationSuggestionDto(BaseModel):
    """DTO for API optimization suggestions."""
    
    request_id: UUID = Field(..., description="API request ID")
    response_id: UUID = Field(..., description="API response ID")
    provider: str = Field(default="ollama", description="LLM provider to use")
    model: Optional[str] = Field(default=None, description="Specific model to use")
    
    @validator('provider')
    def validate_provider(cls, v):
        """Validate LLM provider."""
        valid_providers = ['ollama', 'openai', 'anthropic', 'azure_openai']
        if v.lower() not in valid_providers:
            raise ValueError(f"Invalid provider: {v}")
        return v.lower()


class OptimizationResultDto(BaseModel):
    """DTO for optimization result."""
    
    suggestions: str = Field(..., description="Optimization suggestions")
    request_id: UUID = Field(..., description="API request ID")
    response_id: UUID = Field(..., description="API response ID")
    analysis_timestamp: str = Field(..., description="Analysis timestamp")
    
    class Config:
        """Pydantic configuration."""
        json_encoders = {
            UUID: str
        }
