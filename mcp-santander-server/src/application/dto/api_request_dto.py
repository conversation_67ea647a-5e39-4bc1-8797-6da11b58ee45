"""API Request DTOs for application layer."""

from dataclasses import dataclass
from typing import Any, Dict, Optional
from uuid import UUID

from pydantic import BaseModel, Field, validator


class CreateApiRequestDto(BaseModel):
    """DTO for creating an API request."""
    
    base_url: str = Field(..., description="Base URL of the API")
    path: str = Field(..., description="API endpoint path")
    method: str = Field(..., description="HTTP method")
    headers: Dict[str, str] = Field(default_factory=dict, description="Request headers")
    query_params: Dict[str, Any] = Field(default_factory=dict, description="Query parameters")
    body: Optional[Dict[str, Any]] = Field(default=None, description="Request body")
    timeout: int = Field(default=30, description="Request timeout in seconds")
    max_retries: int = Field(default=3, description="Maximum number of retries")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")
    
    @validator('method')
    def validate_method(cls, v):
        """Validate HTTP method."""
        valid_methods = ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'HEAD', 'OPTIONS']
        if v.upper() not in valid_methods:
            raise ValueError(f"Invalid HTTP method: {v}")
        return v.upper()
    
    @validator('timeout')
    def validate_timeout(cls, v):
        """Validate timeout value."""
        if v <= 0:
            raise ValueError("Timeout must be positive")
        return v
    
    @validator('max_retries')
    def validate_max_retries(cls, v):
        """Validate max retries value."""
        if v < 0:
            raise ValueError("Max retries cannot be negative")
        return v


class ApiRequestResponseDto(BaseModel):
    """DTO for API request response."""
    
    id: UUID = Field(..., description="Request ID")
    base_url: str = Field(..., description="Base URL of the API")
    path: str = Field(..., description="API endpoint path")
    method: str = Field(..., description="HTTP method")
    status: str = Field(..., description="Request status")
    created_at: str = Field(..., description="Creation timestamp")
    updated_at: str = Field(..., description="Last update timestamp")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")
    
    class Config:
        """Pydantic configuration."""
        json_encoders = {
            UUID: str
        }


class ExecuteApiRequestDto(BaseModel):
    """DTO for executing an API request."""
    
    request_id: Optional[UUID] = Field(default=None, description="Existing request ID")
    base_url: str = Field(..., description="Base URL of the API")
    path: str = Field(..., description="API endpoint path")
    method: str = Field(..., description="HTTP method")
    headers: Dict[str, str] = Field(default_factory=dict, description="Request headers")
    query_params: Dict[str, Any] = Field(default_factory=dict, description="Query parameters")
    body: Optional[Dict[str, Any]] = Field(default=None, description="Request body")
    timeout: int = Field(default=30, description="Request timeout in seconds")
    use_cache: bool = Field(default=True, description="Whether to use caching")
    llm_context: Optional[str] = Field(default=None, description="LLM context for assistance")
    
    @validator('method')
    def validate_method(cls, v):
        """Validate HTTP method."""
        valid_methods = ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'HEAD', 'OPTIONS']
        if v.upper() not in valid_methods:
            raise ValueError(f"Invalid HTTP method: {v}")
        return v.upper()


class ApiRequestListDto(BaseModel):
    """DTO for listing API requests."""
    
    limit: int = Field(default=100, description="Maximum number of results")
    offset: int = Field(default=0, description="Number of results to skip")
    status_filter: Optional[str] = Field(default=None, description="Filter by status")
    method_filter: Optional[str] = Field(default=None, description="Filter by HTTP method")
    base_url_filter: Optional[str] = Field(default=None, description="Filter by base URL")
    
    @validator('limit')
    def validate_limit(cls, v):
        """Validate limit value."""
        if v <= 0 or v > 1000:
            raise ValueError("Limit must be between 1 and 1000")
        return v
    
    @validator('offset')
    def validate_offset(cls, v):
        """Validate offset value."""
        if v < 0:
            raise ValueError("Offset cannot be negative")
        return v
