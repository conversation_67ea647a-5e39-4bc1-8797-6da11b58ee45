"""Suggest API Optimization Use Case."""

from uuid import UUID

from ..dto.llm_request_dto import OptimizationSuggestionDto, OptimizationResultDto
from ...domain.services.api_orchestrator import ApiOrchestratorService
from ...domain.ports.repository_port import RepositoryPort


class SuggestApiOptimizationUseCase:
    """
    Use case for suggesting API optimizations using LLM.
    
    This use case analyzes API request/response pairs and uses LLM
    to suggest performance and reliability improvements.
    """
    
    def __init__(
        self, 
        api_orchestrator: ApiOrchestratorService,
        repository: RepositoryPort
    ):
        self._api_orchestrator = api_orchestrator
        self._repository = repository
    
    async def execute(self, dto: OptimizationSuggestionDto) -> OptimizationResultDto:
        """
        Suggest optimizations for an API request/response pair.
        
        Args:
            dto: The optimization request DTO
            
        Returns:
            The optimization suggestions DTO
            
        Raises:
            ValueError: If the request or response is not found
            RuntimeError: If the optimization analysis fails
        """
        # Get the API request and response from repository
        api_request = await self._repository.get_api_request(dto.request_id)
        if not api_request:
            raise ValueError(f"API request not found: {dto.request_id}")
        
        api_response = await self._repository.get_api_response(dto.response_id)
        if not api_response:
            raise ValueError(f"API response not found: {dto.response_id}")
        
        # Verify that the response belongs to the request
        if api_response.request_id != dto.request_id:
            raise ValueError(
                f"Response {dto.response_id} does not belong to request {dto.request_id}"
            )
        
        try:
            # Get optimization suggestions from LLM
            optimization_result = await self._api_orchestrator.suggest_api_optimization(
                request=api_request,
                response=api_response
            )
            
            # Convert to response DTO
            return OptimizationResultDto(
                suggestions=optimization_result["suggestions"],
                request_id=dto.request_id,
                response_id=dto.response_id,
                analysis_timestamp=optimization_result["analysis_timestamp"]
            )
            
        except Exception as e:
            raise RuntimeError(f"Failed to generate optimization suggestions: {str(e)}") from e
