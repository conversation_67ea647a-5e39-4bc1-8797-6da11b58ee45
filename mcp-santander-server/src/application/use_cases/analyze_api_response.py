"""Analyze API Response Use Case."""

from uuid import UUID

from ..dto.llm_request_dto import AnalyzeApiResponseDto, LlmResponseDto
from ...domain.services.api_orchestrator import ApiOrchestratorService
from ...domain.ports.repository_port import RepositoryPort


class AnalyzeApiResponseUseCase:
    """
    Use case for analyzing API responses using LLM.
    
    This use case retrieves an API response and uses LLM
    to provide analysis and insights.
    """
    
    def __init__(
        self, 
        api_orchestrator: ApiOrchestratorService,
        repository: RepositoryPort
    ):
        self._api_orchestrator = api_orchestrator
        self._repository = repository
    
    async def execute(self, dto: AnalyzeApiResponseDto) -> LlmResponseDto:
        """
        Analyze an API response using LLM.
        
        Args:
            dto: The analysis request DTO
            
        Returns:
            The LLM analysis response DTO
            
        Raises:
            ValueError: If the response is not found
            RuntimeError: If the analysis fails
        """
        # Get the API response from repository
        api_response = await self._repository.get_api_response(dto.response_id)
        if not api_response:
            raise ValueError(f"API response not found: {dto.response_id}")
        
        try:
            # Analyze the response with LLM
            analysis_result = await self._api_orchestrator.analyze_api_response_with_llm(
                response=api_response,
                analysis_prompt=dto.analysis_prompt
            )
            
            # Convert to response DTO
            return LlmResponseDto(
                content=analysis_result,
                model=dto.model or "default",
                provider=dto.provider,
                usage={},  # TODO: Add usage tracking
                metadata={
                    "response_id": str(dto.response_id),
                    "analysis_prompt": dto.analysis_prompt,
                    "original_status_code": api_response.status_code,
                    "original_response_time": api_response.response_time_ms
                }
            )
            
        except Exception as e:
            raise RuntimeError(f"Failed to analyze API response: {str(e)}") from e
