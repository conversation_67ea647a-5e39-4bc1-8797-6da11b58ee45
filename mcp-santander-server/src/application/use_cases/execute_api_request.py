"""Execute API Request Use Case."""

from typing import Optional
from uuid import UUID

from ..dto.api_request_dto import ExecuteApiRequestDto, ApiRequestResponseDto
from ..dto.llm_request_dto import LlmResponseDto
from ...domain.entities.api_request import ApiRequest
from ...domain.entities.api_response import ApiResponse
from ...domain.value_objects.api_endpoint import ApiEndpoint
from ...domain.value_objects.request_method import RequestMethod
from ...domain.services.api_orchestrator import ApiOrchestratorService


class ExecuteApiRequestUseCase:
    """
    Use case for executing API requests with optional LLM assistance.
    
    This use case orchestrates the execution of API requests,
    including caching, retry logic, and LLM-assisted error handling.
    """
    
    def __init__(self, api_orchestrator: ApiOrchestratorService):
        self._api_orchestrator = api_orchestrator
    
    async def execute(self, dto: ExecuteApiRequestDto) -> ApiRequestResponseDto:
        """
        Execute an API request.
        
        Args:
            dto: The execution request DTO
            
        Returns:
            The API request response DTO
            
        Raises:
            ValueError: If the request is invalid
            RuntimeError: If the execution fails
        """
        # Create or get existing API request
        if dto.request_id:
            # TODO: Implement getting existing request from repository
            raise NotImplementedError("Getting existing request not implemented yet")
        else:
            api_request = self._create_api_request_from_dto(dto)
        
        try:
            # Execute the request with orchestrator
            response = await self._api_orchestrator.execute_api_request_with_llm_assistance(
                request=api_request,
                llm_context=dto.llm_context,
                use_cache=dto.use_cache
            )
            
            # Convert to response DTO
            return self._convert_to_response_dto(api_request, response)
            
        except Exception as e:
            # Mark request as failed and re-raise
            api_request.mark_as_failed(str(e))
            raise RuntimeError(f"Failed to execute API request: {str(e)}") from e
    
    def _create_api_request_from_dto(self, dto: ExecuteApiRequestDto) -> ApiRequest:
        """Create an API request entity from DTO."""
        endpoint = ApiEndpoint(
            base_url=dto.base_url,
            path=dto.path
        )
        
        method = RequestMethod.from_string(dto.method)
        
        return ApiRequest(
            endpoint=endpoint,
            method=method,
            headers=dto.headers,
            query_params=dto.query_params,
            body=dto.body,
            timeout=dto.timeout
        )
    
    def _convert_to_response_dto(
        self, 
        request: ApiRequest, 
        response: ApiResponse
    ) -> ApiRequestResponseDto:
        """Convert entities to response DTO."""
        return ApiRequestResponseDto(
            id=request.id,
            base_url=request.endpoint.base_url,
            path=request.endpoint.path,
            method=str(request.method),
            status=str(request.status),
            created_at=request.created_at.isoformat(),
            updated_at=request.updated_at.isoformat(),
            metadata={
                **request.metadata,
                "response_id": str(response.id),
                "response_status_code": response.status_code,
                "response_time_ms": response.response_time_ms,
                "response_successful": response.is_successful()
            }
        )
