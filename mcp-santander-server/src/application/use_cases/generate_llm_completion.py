"""Generate LLM Completion Use Case."""

from typing import As<PERSON><PERSON><PERSON><PERSON>

from ..dto.llm_request_dto import LlmCompletionDto, LlmResponseDto
from ...domain.entities.llm_request import LlmRequest, LlmMessage
from ...domain.value_objects.llm_provider import <PERSON><PERSON><PERSON>rovider
from ...domain.ports.llm_client_port import LlmClientPort
from ...domain.ports.repository_port import RepositoryPort


class GenerateLlmCompletionUseCase:
    """
    Use case for generating LLM completions.
    
    This use case handles direct LLM completion requests,
    including both streaming and non-streaming responses.
    """
    
    def __init__(
        self, 
        llm_client: LlmClientPort,
        repository: RepositoryPort
    ):
        self._llm_client = llm_client
        self._repository = repository
    
    async def execute(self, dto: LlmCompletionDto) -> LlmResponseDto:
        """
        Generate an LLM completion.
        
        Args:
            dto: The completion request DTO
            
        Returns:
            The LLM response DTO
            
        Raises:
            ValueError: If the request is invalid
            RuntimeError: If the completion fails
        """
        # Create LLM request entity
        llm_request = self._create_llm_request_from_dto(dto)
        
        try:
            # Save request to repository
            await self._repository.save_llm_request(llm_request)
            
            # Mark as processing
            llm_request.mark_as_processing()
            await self._repository.save_llm_request(llm_request)
            
            # Generate completion
            if dto.stream:
                # For streaming, we'll collect all chunks and return the full response
                # In a real implementation, you might want to handle streaming differently
                content_chunks = []
                async for chunk in self._llm_client.generate_streaming_completion(llm_request):
                    content_chunks.append(chunk)
                
                # Create response from collected chunks
                full_content = "".join(content_chunks)
                llm_response = type('LlmResponse', (), {
                    'content': full_content,
                    'model': dto.model,
                    'provider': self._llm_client.get_provider(),
                    'usage': {},
                    'metadata': {'streaming': True}
                })()
            else:
                # Non-streaming completion
                llm_response = await self._llm_client.generate_completion(llm_request)
            
            # Mark as completed
            llm_request.mark_as_completed()
            await self._repository.save_llm_request(llm_request)
            
            # Convert to response DTO
            return LlmResponseDto(
                content=llm_response.content,
                model=llm_response.model,
                provider=str(llm_response.provider),
                usage=llm_response.usage,
                metadata={
                    **llm_response.metadata,
                    "request_id": str(llm_request.id),
                    "message_count": len(llm_request.messages),
                    "estimated_tokens": llm_request.get_total_tokens_estimate()
                }
            )
            
        except Exception as e:
            # Mark request as failed
            llm_request.mark_as_failed(str(e))
            await self._repository.save_llm_request(llm_request)
            raise RuntimeError(f"Failed to generate LLM completion: {str(e)}") from e
    
    async def execute_streaming(self, dto: LlmCompletionDto) -> AsyncIterator[str]:
        """
        Generate a streaming LLM completion.
        
        Args:
            dto: The completion request DTO
            
        Yields:
            Chunks of the completion text
            
        Raises:
            ValueError: If the request is invalid
            RuntimeError: If the completion fails
        """
        # Create LLM request entity
        llm_request = self._create_llm_request_from_dto(dto)
        
        try:
            # Save request to repository
            await self._repository.save_llm_request(llm_request)
            
            # Mark as processing
            llm_request.mark_as_processing()
            await self._repository.save_llm_request(llm_request)
            
            # Generate streaming completion
            async for chunk in self._llm_client.generate_streaming_completion(llm_request):
                yield chunk
            
            # Mark as completed
            llm_request.mark_as_completed()
            await self._repository.save_llm_request(llm_request)
            
        except Exception as e:
            # Mark request as failed
            llm_request.mark_as_failed(str(e))
            await self._repository.save_llm_request(llm_request)
            raise RuntimeError(f"Failed to generate streaming LLM completion: {str(e)}") from e
    
    def _create_llm_request_from_dto(self, dto: LlmCompletionDto) -> LlmRequest:
        """Create an LLM request entity from DTO."""
        provider = LlmProvider.from_string(dto.provider)
        
        # Convert DTO messages to domain messages
        messages = [
            LlmMessage(
                role=msg.role,
                content=msg.content,
                metadata=msg.metadata
            )
            for msg in dto.messages
        ]
        
        return LlmRequest(
            provider=provider,
            model=dto.model,
            messages=messages,
            temperature=dto.temperature,
            max_tokens=dto.max_tokens,
            stream=dto.stream
        )
