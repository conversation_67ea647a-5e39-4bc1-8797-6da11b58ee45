"""OpenAI LLM client adapter."""

from typing import Any, Async<PERSON><PERSON><PERSON>, Dict, List, Optional

import structlog
from openai import Async<PERSON>penA<PERSON>

from ....domain.entities.llm_request import LlmRequest, LlmMessage
from ....domain.ports.llm_client_port import L<PERSON><PERSON><PERSON><PERSON><PERSON>, LlmResponse
from ....domain.value_objects.llm_provider import LlmProvider


logger = structlog.get_logger(__name__)


class OpenAIClient(LlmClientPort):
    """
    OpenAI LLM client adapter.
    
    This adapter implements the LlmClientPort interface for OpenAI,
    providing remote LLM capabilities.
    """
    
    def __init__(
        self,
        api_key: str,
        model: str = "gpt-4",
        timeout: int = 60,
        base_url: Optional[str] = None
    ):
        self._api_key = api_key
        self._model = model
        self._timeout = timeout
        self._client = AsyncOpenAI(
            api_key=api_key,
            timeout=timeout,
            base_url=base_url
        )
        self._logger = logger.bind(client="openai", model=model)
    
    async def generate_completion(self, request: LlmRequest) -> LlmResponse:
        """
        Generate a completion for the given request.
        
        Args:
            request: The LLM request to process
            
        Returns:
            The LLM response
            
        Raises:
            LlmClientError: If the request fails
        """
        self._logger.info("Generating completion", request_id=str(request.id))
        
        try:
            # Prepare messages for OpenAI format
            messages = self._prepare_messages(request.messages)
            
            # Make the API call
            response = await self._client.chat.completions.create(
                model=request.model,
                messages=messages,
                temperature=request.temperature,
                max_tokens=request.max_tokens,
                stream=False
            )
            
            # Extract the response content
            content = response.choices[0].message.content
            
            self._logger.info(
                "Completion generated successfully",
                request_id=str(request.id),
                response_tokens=response.usage.completion_tokens if response.usage else 0
            )
            
            return LlmResponse(
                content=content,
                model=response.model,
                provider=LlmProvider.OPENAI,
                usage={
                    "prompt_tokens": response.usage.prompt_tokens if response.usage else 0,
                    "completion_tokens": response.usage.completion_tokens if response.usage else 0,
                    "total_tokens": response.usage.total_tokens if response.usage else 0
                },
                metadata={
                    "finish_reason": response.choices[0].finish_reason,
                    "response_id": response.id,
                    "created": response.created
                }
            )
            
        except Exception as e:
            self._logger.error(
                "Error during completion",
                request_id=str(request.id),
                error=str(e)
            )
            raise RuntimeError(f"OpenAI client error: {str(e)}") from e
    
    async def generate_streaming_completion(
        self, 
        request: LlmRequest
    ) -> AsyncIterator[str]:
        """
        Generate a streaming completion for the given request.
        
        Args:
            request: The LLM request to process
            
        Yields:
            Chunks of the completion text
            
        Raises:
            LlmClientError: If the request fails
        """
        self._logger.info("Generating streaming completion", request_id=str(request.id))
        
        try:
            # Prepare messages for OpenAI format
            messages = self._prepare_messages(request.messages)
            
            # Make the streaming API call
            stream = await self._client.chat.completions.create(
                model=request.model,
                messages=messages,
                temperature=request.temperature,
                max_tokens=request.max_tokens,
                stream=True
            )
            
            async for chunk in stream:
                if chunk.choices and chunk.choices[0].delta.content:
                    yield chunk.choices[0].delta.content
            
            self._logger.info(
                "Streaming completion finished",
                request_id=str(request.id)
            )
            
        except Exception as e:
            self._logger.error(
                "Error during streaming completion",
                request_id=str(request.id),
                error=str(e)
            )
            raise RuntimeError(f"OpenAI client error: {str(e)}") from e
    
    async def generate_chat_completion(
        self,
        messages: List[LlmMessage],
        model: str,
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        **kwargs: Any
    ) -> LlmResponse:
        """
        Generate a chat completion for the given messages.
        
        Args:
            messages: List of conversation messages
            model: Model to use for completion
            temperature: Sampling temperature
            max_tokens: Maximum tokens to generate
            **kwargs: Additional provider-specific parameters
            
        Returns:
            The LLM response
        """
        # Create a temporary request
        request = LlmRequest(
            provider=LlmProvider.OPENAI,
            model=model,
            messages=messages,
            temperature=temperature,
            max_tokens=max_tokens
        )
        
        return await self.generate_completion(request)
    
    async def get_available_models(self) -> List[str]:
        """
        Get list of available models for this provider.
        
        Returns:
            List of model names
        """
        try:
            models = await self._client.models.list()
            model_names = [model.id for model in models.data if "gpt" in model.id.lower()]
            
            self._logger.info("Retrieved available models", count=len(model_names))
            return model_names
            
        except Exception as e:
            self._logger.error("Failed to get available models", error=str(e))
            # Return default models if API call fails
            return ["gpt-4", "gpt-3.5-turbo"]
    
    async def health_check(self) -> bool:
        """
        Check if the LLM client is healthy and can make requests.
        
        Returns:
            True if healthy, False otherwise
        """
        try:
            # Try to list models as a health check
            await self._client.models.list()
            return True
        except Exception:
            return False
    
    async def get_client_info(self) -> Dict[str, Any]:
        """
        Get information about the LLM client.
        
        Returns:
            Dictionary with client information
        """
        return {
            "provider": "openai",
            "model": self._model,
            "timeout": self._timeout,
            "supports_streaming": True,
            "supports_chat": True,
            "api_key_configured": bool(self._api_key)
        }
    
    def get_provider(self) -> LlmProvider:
        """
        Get the LLM provider for this client.
        
        Returns:
            The LLM provider
        """
        return LlmProvider.OPENAI
    
    def _prepare_messages(self, messages: List[LlmMessage]) -> List[Dict[str, str]]:
        """Prepare messages for OpenAI format."""
        return [
            {
                "role": msg.role,
                "content": msg.content
            }
            for msg in messages
        ]
