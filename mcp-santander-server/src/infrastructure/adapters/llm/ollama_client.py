"""Ollama LLM client adapter."""

import asyncio
import json
from typing import Any, AsyncIterator, Dict, List, Optional

import httpx
import structlog

from ....domain.entities.llm_request import LlmRequest, LlmMessage
from ....domain.ports.llm_client_port import Llm<PERSON>lient<PERSON>ort, LlmResponse
from ....domain.value_objects.llm_provider import LlmProvider


logger = structlog.get_logger(__name__)


class OllamaClient(LlmClientPort):
    """
    Ollama LLM client adapter.
    
    This adapter implements the LlmClientPort interface for Ollama,
    providing local LLM capabilities.
    """
    
    def __init__(
        self,
        base_url: str = "http://localhost:11434",
        model: str = "llama3.2:latest",
        timeout: int = 60
    ):
        self._base_url = base_url.rstrip("/")
        self._model = model
        self._timeout = timeout
        self._client = httpx.AsyncClient(timeout=timeout)
        self._logger = logger.bind(client="ollama", base_url=base_url, model=model)
    
    async def generate_completion(self, request: LlmRequest) -> LlmResponse:
        """
        Generate a completion for the given request.
        
        Args:
            request: The LLM request to process
            
        Returns:
            The LLM response
            
        Raises:
            LlmClientError: If the request fails
        """
        self._logger.info("Generating completion", request_id=str(request.id))
        
        try:
            # Prepare the request payload
            payload = self._prepare_chat_payload(request)
            
            # Make the API call
            response = await self._client.post(
                f"{self._base_url}/api/chat",
                json=payload
            )
            response.raise_for_status()
            
            # Parse the response
            response_data = response.json()
            
            self._logger.info(
                "Completion generated successfully",
                request_id=str(request.id),
                response_tokens=response_data.get("eval_count", 0)
            )
            
            return LlmResponse(
                content=response_data["message"]["content"],
                model=response_data.get("model", self._model),
                provider=LlmProvider.OLLAMA,
                usage={
                    "prompt_tokens": response_data.get("prompt_eval_count", 0),
                    "completion_tokens": response_data.get("eval_count", 0),
                    "total_tokens": (
                        response_data.get("prompt_eval_count", 0) +
                        response_data.get("eval_count", 0)
                    )
                },
                metadata={
                    "eval_duration": response_data.get("eval_duration", 0),
                    "load_duration": response_data.get("load_duration", 0),
                    "prompt_eval_duration": response_data.get("prompt_eval_duration", 0)
                }
            )
            
        except httpx.HTTPStatusError as e:
            self._logger.error(
                "HTTP error during completion",
                request_id=str(request.id),
                status_code=e.response.status_code,
                error=str(e)
            )
            raise RuntimeError(f"Ollama API error: {e.response.status_code}") from e
        except Exception as e:
            self._logger.error(
                "Unexpected error during completion",
                request_id=str(request.id),
                error=str(e)
            )
            raise RuntimeError(f"Ollama client error: {str(e)}") from e
    
    async def generate_streaming_completion(
        self, 
        request: LlmRequest
    ) -> AsyncIterator[str]:
        """
        Generate a streaming completion for the given request.
        
        Args:
            request: The LLM request to process
            
        Yields:
            Chunks of the completion text
            
        Raises:
            LlmClientError: If the request fails
        """
        self._logger.info("Generating streaming completion", request_id=str(request.id))
        
        try:
            # Prepare the request payload with streaming enabled
            payload = self._prepare_chat_payload(request)
            payload["stream"] = True
            
            # Make the streaming API call
            async with self._client.stream(
                "POST",
                f"{self._base_url}/api/chat",
                json=payload
            ) as response:
                response.raise_for_status()
                
                async for line in response.aiter_lines():
                    if line.strip():
                        try:
                            chunk_data = json.loads(line)
                            if "message" in chunk_data and "content" in chunk_data["message"]:
                                content = chunk_data["message"]["content"]
                                if content:
                                    yield content
                        except json.JSONDecodeError:
                            # Skip invalid JSON lines
                            continue
            
            self._logger.info(
                "Streaming completion finished",
                request_id=str(request.id)
            )
            
        except httpx.HTTPStatusError as e:
            self._logger.error(
                "HTTP error during streaming completion",
                request_id=str(request.id),
                status_code=e.response.status_code,
                error=str(e)
            )
            raise RuntimeError(f"Ollama API error: {e.response.status_code}") from e
        except Exception as e:
            self._logger.error(
                "Unexpected error during streaming completion",
                request_id=str(request.id),
                error=str(e)
            )
            raise RuntimeError(f"Ollama client error: {str(e)}") from e
    
    async def generate_chat_completion(
        self,
        messages: List[LlmMessage],
        model: str,
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        **kwargs: Any
    ) -> LlmResponse:
        """
        Generate a chat completion for the given messages.
        
        Args:
            messages: List of conversation messages
            model: Model to use for completion
            temperature: Sampling temperature
            max_tokens: Maximum tokens to generate
            **kwargs: Additional provider-specific parameters
            
        Returns:
            The LLM response
        """
        # Create a temporary request
        request = LlmRequest(
            provider=LlmProvider.OLLAMA,
            model=model,
            messages=messages,
            temperature=temperature,
            max_tokens=max_tokens
        )
        
        return await self.generate_completion(request)
    
    async def get_available_models(self) -> List[str]:
        """
        Get list of available models for this provider.
        
        Returns:
            List of model names
        """
        try:
            response = await self._client.get(f"{self._base_url}/api/tags")
            response.raise_for_status()
            
            data = response.json()
            models = [model["name"] for model in data.get("models", [])]
            
            self._logger.info("Retrieved available models", count=len(models))
            return models
            
        except Exception as e:
            self._logger.error("Failed to get available models", error=str(e))
            # Return default model if API call fails
            return [self._model]
    
    async def health_check(self) -> bool:
        """
        Check if the LLM client is healthy and can make requests.
        
        Returns:
            True if healthy, False otherwise
        """
        try:
            response = await self._client.get(f"{self._base_url}/api/tags")
            return response.status_code == 200
        except Exception:
            return False
    
    async def get_client_info(self) -> Dict[str, Any]:
        """
        Get information about the LLM client.
        
        Returns:
            Dictionary with client information
        """
        return {
            "provider": "ollama",
            "base_url": self._base_url,
            "model": self._model,
            "timeout": self._timeout,
            "supports_streaming": True,
            "supports_chat": True
        }
    
    def get_provider(self) -> LlmProvider:
        """
        Get the LLM provider for this client.
        
        Returns:
            The LLM provider
        """
        return LlmProvider.OLLAMA
    
    def _prepare_chat_payload(self, request: LlmRequest) -> Dict[str, Any]:
        """Prepare the chat API payload."""
        # Convert domain messages to Ollama format
        messages = []
        for msg in request.messages:
            messages.append({
                "role": msg.role,
                "content": msg.content
            })
        
        payload = {
            "model": request.model,
            "messages": messages,
            "options": {
                "temperature": request.temperature
            }
        }
        
        if request.max_tokens:
            payload["options"]["num_predict"] = request.max_tokens
        
        return payload
    
    async def __aenter__(self):
        """Async context manager entry."""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self._client.aclose()
