"""LLM Client Factory for managing multiple LLM providers."""

from typing import Dict, Optional

import structlog

from ....domain.ports.llm_client_port import Llm<PERSON>lientPort
from ....domain.value_objects.llm_provider import <PERSON>lm<PERSON>rovider
from ...config.settings import Settings
from .ollama_client import OllamaClient
from .openai_client import OpenAIClient


logger = structlog.get_logger(__name__)


class LlmClientFactory:
    """
    Factory for creating and managing LLM clients.
    
    This factory manages multiple LLM client instances and provides
    a unified interface for accessing different providers.
    """
    
    def __init__(
        self,
        ollama_client: Optional[OllamaClient] = None,
        openai_client: Optional[OpenAIClient] = None,
        settings: Optional[Settings] = None
    ):
        self._clients: Dict[str, LlmClientPort] = {}
        self._settings = settings
        self._logger = logger.bind(component="llm_client_factory")
        
        # Register provided clients
        if ollama_client:
            self._clients["ollama"] = ollama_client
        if openai_client:
            self._clients["openai"] = openai_client
    
    def get_client(self, provider: str) -> LlmClientPort:
        """
        Get an LLM client for the specified provider.
        
        Args:
            provider: The LLM provider name
            
        Returns:
            The LLM client instance
            
        Raises:
            ValueError: If the provider is not supported or not configured
        """
        provider = provider.lower()
        
        # Check if client is already created
        if provider in self._clients:
            return self._clients[provider]
        
        # Create client if not exists
        client = self._create_client(provider)
        if client:
            self._clients[provider] = client
            return client
        
        raise ValueError(f"Unsupported or unconfigured LLM provider: {provider}")
    
    def get_available_providers(self) -> list[str]:
        """
        Get list of available LLM providers.
        
        Returns:
            List of provider names
        """
        available = list(self._clients.keys())
        
        # Add providers that can be created from settings
        if self._settings:
            enabled_providers = self._settings.get_enabled_llm_providers()
            for provider in enabled_providers:
                if provider not in available:
                    available.append(provider)
        
        return available
    
    def get_primary_client(self) -> LlmClientPort:
        """
        Get the primary LLM client (Ollama by default).
        
        Returns:
            The primary LLM client
            
        Raises:
            RuntimeError: If no clients are available
        """
        # Try Ollama first (local)
        if "ollama" in self._clients:
            return self._clients["ollama"]
        
        # Try to create Ollama client
        try:
            return self.get_client("ollama")
        except ValueError:
            pass
        
        # Fall back to any available client
        if self._clients:
            return next(iter(self._clients.values()))
        
        raise RuntimeError("No LLM clients available")
    
    async def health_check_all(self) -> Dict[str, bool]:
        """
        Perform health check on all clients.
        
        Returns:
            Dictionary mapping provider names to health status
        """
        results = {}
        
        for provider, client in self._clients.items():
            try:
                results[provider] = await client.health_check()
            except Exception as e:
                self._logger.error(
                    "Health check failed for provider",
                    provider=provider,
                    error=str(e)
                )
                results[provider] = False
        
        return results
    
    def _create_client(self, provider: str) -> Optional[LlmClientPort]:
        """Create a client for the specified provider."""
        if not self._settings:
            self._logger.warning(
                "No settings available, cannot create client",
                provider=provider
            )
            return None
        
        provider_settings = self._settings.get_llm_provider_settings(provider)
        if not provider_settings or not provider_settings.enabled:
            self._logger.warning(
                "Provider not enabled or configured",
                provider=provider
            )
            return None
        
        try:
            if provider == "ollama":
                return OllamaClient(
                    base_url=provider_settings.base_url,
                    model=provider_settings.model,
                    timeout=provider_settings.timeout
                )
            elif provider == "openai":
                if not provider_settings.api_key:
                    self._logger.error(
                        "OpenAI API key not configured",
                        provider=provider
                    )
                    return None
                
                return OpenAIClient(
                    api_key=provider_settings.api_key,
                    model=provider_settings.model,
                    timeout=provider_settings.timeout
                )
            else:
                self._logger.error(
                    "Unsupported provider",
                    provider=provider
                )
                return None
                
        except Exception as e:
            self._logger.error(
                "Failed to create client",
                provider=provider,
                error=str(e)
            )
            return None
