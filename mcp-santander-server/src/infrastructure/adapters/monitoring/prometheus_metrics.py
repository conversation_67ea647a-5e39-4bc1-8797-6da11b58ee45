"""Prometheus metrics adapter."""

import time
from typing import Any, Dict, Optional
import threading

from prometheus_client import (
    Counter, Histogram, Gauge, Info, 
    start_http_server, CollectorRegistry, REGISTRY
)
import structlog


logger = structlog.get_logger(__name__)


class PrometheusMetrics:
    """
    Prometheus metrics adapter for monitoring application performance.
    
    This adapter provides metrics collection and exposure for monitoring
    API requests, LLM operations, cache usage, and system health.
    """
    
    def __init__(
        self,
        port: int = 8080,
        enabled: bool = True,
        registry: Optional[CollectorRegistry] = None
    ):
        self._port = port
        self._enabled = enabled
        self._registry = registry or REGISTRY
        self._server_started = False
        self._lock = threading.Lock()
        self._logger = logger.bind(component="prometheus_metrics", port=port)
        
        if self._enabled:
            self._setup_metrics()
    
    def _setup_metrics(self):
        """Set up Prometheus metrics."""
        # API Request Metrics
        self.api_requests_total = Counter(
            'mcp_api_requests_total',
            'Total number of API requests',
            ['method', 'endpoint', 'status_code'],
            registry=self._registry
        )
        
        self.api_request_duration = Histogram(
            'mcp_api_request_duration_seconds',
            'API request duration in seconds',
            ['method', 'endpoint'],
            registry=self._registry
        )
        
        self.api_request_size = Histogram(
            'mcp_api_request_size_bytes',
            'API request size in bytes',
            ['method', 'endpoint'],
            registry=self._registry
        )
        
        self.api_response_size = Histogram(
            'mcp_api_response_size_bytes',
            'API response size in bytes',
            ['method', 'endpoint'],
            registry=self._registry
        )
        
        # LLM Metrics
        self.llm_requests_total = Counter(
            'mcp_llm_requests_total',
            'Total number of LLM requests',
            ['provider', 'model', 'status'],
            registry=self._registry
        )
        
        self.llm_request_duration = Histogram(
            'mcp_llm_request_duration_seconds',
            'LLM request duration in seconds',
            ['provider', 'model'],
            registry=self._registry
        )
        
        self.llm_tokens_total = Counter(
            'mcp_llm_tokens_total',
            'Total number of LLM tokens processed',
            ['provider', 'model', 'type'],  # type: prompt, completion
            registry=self._registry
        )
        
        # Cache Metrics
        self.cache_operations_total = Counter(
            'mcp_cache_operations_total',
            'Total number of cache operations',
            ['operation', 'result'],  # operation: get, set, delete; result: hit, miss, success, error
            registry=self._registry
        )
        
        self.cache_size = Gauge(
            'mcp_cache_size',
            'Current cache size',
            ['cache_type'],
            registry=self._registry
        )
        
        # System Metrics
        self.active_connections = Gauge(
            'mcp_active_connections',
            'Number of active connections',
            registry=self._registry
        )
        
        self.system_info = Info(
            'mcp_system_info',
            'System information',
            registry=self._registry
        )
        
        # Error Metrics
        self.errors_total = Counter(
            'mcp_errors_total',
            'Total number of errors',
            ['component', 'error_type'],
            registry=self._registry
        )
        
        self._logger.info("Prometheus metrics initialized")
    
    def start_server(self):
        """Start the Prometheus metrics HTTP server."""
        if not self._enabled:
            return
        
        with self._lock:
            if not self._server_started:
                try:
                    start_http_server(self._port, registry=self._registry)
                    self._server_started = True
                    self._logger.info("Prometheus metrics server started", port=self._port)
                except Exception as e:
                    self._logger.error("Failed to start metrics server", error=str(e))
    
    def record_api_request(
        self,
        method: str,
        endpoint: str,
        status_code: int,
        duration: float,
        request_size: Optional[int] = None,
        response_size: Optional[int] = None
    ):
        """Record API request metrics."""
        if not self._enabled:
            return
        
        try:
            # Record request count
            self.api_requests_total.labels(
                method=method,
                endpoint=endpoint,
                status_code=str(status_code)
            ).inc()
            
            # Record duration
            self.api_request_duration.labels(
                method=method,
                endpoint=endpoint
            ).observe(duration)
            
            # Record sizes if provided
            if request_size is not None:
                self.api_request_size.labels(
                    method=method,
                    endpoint=endpoint
                ).observe(request_size)
            
            if response_size is not None:
                self.api_response_size.labels(
                    method=method,
                    endpoint=endpoint
                ).observe(response_size)
                
        except Exception as e:
            self._logger.error("Error recording API request metrics", error=str(e))
    
    def record_llm_request(
        self,
        provider: str,
        model: str,
        status: str,
        duration: float,
        prompt_tokens: Optional[int] = None,
        completion_tokens: Optional[int] = None
    ):
        """Record LLM request metrics."""
        if not self._enabled:
            return
        
        try:
            # Record request count
            self.llm_requests_total.labels(
                provider=provider,
                model=model,
                status=status
            ).inc()
            
            # Record duration
            self.llm_request_duration.labels(
                provider=provider,
                model=model
            ).observe(duration)
            
            # Record token usage
            if prompt_tokens is not None:
                self.llm_tokens_total.labels(
                    provider=provider,
                    model=model,
                    type="prompt"
                ).inc(prompt_tokens)
            
            if completion_tokens is not None:
                self.llm_tokens_total.labels(
                    provider=provider,
                    model=model,
                    type="completion"
                ).inc(completion_tokens)
                
        except Exception as e:
            self._logger.error("Error recording LLM request metrics", error=str(e))
    
    def record_cache_operation(
        self,
        operation: str,
        result: str,
        cache_type: str = "default"
    ):
        """Record cache operation metrics."""
        if not self._enabled:
            return
        
        try:
            self.cache_operations_total.labels(
                operation=operation,
                result=result
            ).inc()
            
        except Exception as e:
            self._logger.error("Error recording cache operation metrics", error=str(e))
    
    def update_cache_size(self, size: int, cache_type: str = "default"):
        """Update cache size metric."""
        if not self._enabled:
            return
        
        try:
            self.cache_size.labels(cache_type=cache_type).set(size)
        except Exception as e:
            self._logger.error("Error updating cache size metric", error=str(e))
    
    def update_active_connections(self, count: int):
        """Update active connections metric."""
        if not self._enabled:
            return
        
        try:
            self.active_connections.set(count)
        except Exception as e:
            self._logger.error("Error updating active connections metric", error=str(e))
    
    def record_error(self, component: str, error_type: str):
        """Record error metrics."""
        if not self._enabled:
            return
        
        try:
            self.errors_total.labels(
                component=component,
                error_type=error_type
            ).inc()
        except Exception as e:
            self._logger.error("Error recording error metrics", error=str(e))
    
    def set_system_info(self, info: Dict[str, str]):
        """Set system information."""
        if not self._enabled:
            return
        
        try:
            self.system_info.info(info)
        except Exception as e:
            self._logger.error("Error setting system info", error=str(e))
    
    def get_metrics_summary(self) -> Dict[str, Any]:
        """Get a summary of current metrics."""
        if not self._enabled:
            return {"enabled": False}
        
        try:
            # This is a simplified summary - in a real implementation,
            # you might want to collect actual metric values
            return {
                "enabled": True,
                "port": self._port,
                "server_started": self._server_started,
                "metrics_available": [
                    "api_requests_total",
                    "api_request_duration",
                    "llm_requests_total",
                    "llm_request_duration",
                    "llm_tokens_total",
                    "cache_operations_total",
                    "cache_size",
                    "active_connections",
                    "errors_total"
                ]
            }
        except Exception as e:
            self._logger.error("Error getting metrics summary", error=str(e))
            return {"enabled": True, "error": str(e)}
