"""In-memory cache adapter."""

import asyncio
import time
from typing import Any, Dict, Optional
import threading

import structlog

from ....domain.ports.cache_port import CachePort


logger = structlog.get_logger(__name__)


class CacheEntry:
    """Cache entry with TTL support."""
    
    def __init__(self, value: Any, ttl: Optional[int] = None):
        self.value = value
        self.created_at = time.time()
        self.ttl = ttl
    
    def is_expired(self) -> bool:
        """Check if the cache entry is expired."""
        if self.ttl is None:
            return False
        return time.time() - self.created_at > self.ttl
    
    def get_remaining_ttl(self) -> Optional[int]:
        """Get remaining TTL in seconds."""
        if self.ttl is None:
            return None
        
        elapsed = time.time() - self.created_at
        remaining = self.ttl - elapsed
        return max(0, int(remaining))


class MemoryCache(CachePort):
    """
    In-memory cache adapter.
    
    This adapter implements the CachePort interface using
    an in-memory dictionary with TTL support.
    """
    
    def __init__(self, default_ttl: int = 3600, cleanup_interval: int = 300):
        self._cache: Dict[str, CacheEntry] = {}
        self._default_ttl = default_ttl
        self._cleanup_interval = cleanup_interval
        self._lock = threading.RLock()
        self._stats = {
            "hits": 0,
            "misses": 0,
            "sets": 0,
            "deletes": 0,
            "cleanups": 0
        }
        self._logger = logger.bind(cache="memory")
        
        # Start cleanup task
        self._cleanup_task = None
        self._start_cleanup_task()
    
    async def get(self, key: str) -> Optional[Any]:
        """
        Get a value from the cache.
        
        Args:
            key: The cache key
            
        Returns:
            The cached value or None if not found
        """
        with self._lock:
            entry = self._cache.get(key)
            
            if entry is None:
                self._stats["misses"] += 1
                self._logger.debug("Cache miss", key=key)
                return None
            
            if entry.is_expired():
                del self._cache[key]
                self._stats["misses"] += 1
                self._logger.debug("Cache miss (expired)", key=key)
                return None
            
            self._stats["hits"] += 1
            self._logger.debug("Cache hit", key=key)
            return entry.value
    
    async def set(
        self, 
        key: str, 
        value: Any, 
        ttl: Optional[int] = None
    ) -> bool:
        """
        Set a value in the cache.
        
        Args:
            key: The cache key
            value: The value to cache
            ttl: Time to live in seconds (None for default)
            
        Returns:
            True if successful, False otherwise
        """
        try:
            effective_ttl = ttl if ttl is not None else self._default_ttl
            
            with self._lock:
                self._cache[key] = CacheEntry(value, effective_ttl)
                self._stats["sets"] += 1
            
            self._logger.debug(
                "Cache set",
                key=key,
                ttl=effective_ttl,
                cache_size=len(self._cache)
            )
            return True
            
        except Exception as e:
            self._logger.error("Cache set error", key=key, error=str(e))
            return False
    
    async def delete(self, key: str) -> bool:
        """
        Delete a value from the cache.
        
        Args:
            key: The cache key
            
        Returns:
            True if the key was deleted, False if not found
        """
        with self._lock:
            if key in self._cache:
                del self._cache[key]
                self._stats["deletes"] += 1
                self._logger.debug("Cache delete", key=key)
                return True
            
            self._logger.debug("Cache delete (not found)", key=key)
            return False
    
    async def exists(self, key: str) -> bool:
        """
        Check if a key exists in the cache.
        
        Args:
            key: The cache key
            
        Returns:
            True if the key exists, False otherwise
        """
        with self._lock:
            entry = self._cache.get(key)
            if entry is None:
                return False
            
            if entry.is_expired():
                del self._cache[key]
                return False
            
            return True
    
    async def clear(self) -> bool:
        """
        Clear all values from the cache.
        
        Returns:
            True if successful, False otherwise
        """
        try:
            with self._lock:
                cache_size = len(self._cache)
                self._cache.clear()
            
            self._logger.info("Cache cleared", previous_size=cache_size)
            return True
            
        except Exception as e:
            self._logger.error("Cache clear error", error=str(e))
            return False
    
    async def get_ttl(self, key: str) -> Optional[int]:
        """
        Get the time to live for a key.
        
        Args:
            key: The cache key
            
        Returns:
            TTL in seconds or None if key doesn't exist or has no TTL
        """
        with self._lock:
            entry = self._cache.get(key)
            if entry is None:
                return None
            
            if entry.is_expired():
                del self._cache[key]
                return None
            
            return entry.get_remaining_ttl()
    
    async def set_ttl(self, key: str, ttl: int) -> bool:
        """
        Set the time to live for an existing key.
        
        Args:
            key: The cache key
            ttl: Time to live in seconds
            
        Returns:
            True if successful, False if key doesn't exist
        """
        with self._lock:
            entry = self._cache.get(key)
            if entry is None:
                return False
            
            if entry.is_expired():
                del self._cache[key]
                return False
            
            # Create new entry with updated TTL
            self._cache[key] = CacheEntry(entry.value, ttl)
            return True
    
    async def get_stats(self) -> Dict[str, Any]:
        """
        Get cache statistics.
        
        Returns:
            Dictionary with cache statistics
        """
        with self._lock:
            cache_size = len(self._cache)
            
            # Calculate hit rate
            total_requests = self._stats["hits"] + self._stats["misses"]
            hit_rate = (
                self._stats["hits"] / total_requests 
                if total_requests > 0 else 0.0
            )
            
            return {
                "type": "memory",
                "size": cache_size,
                "hits": self._stats["hits"],
                "misses": self._stats["misses"],
                "sets": self._stats["sets"],
                "deletes": self._stats["deletes"],
                "cleanups": self._stats["cleanups"],
                "hit_rate": hit_rate,
                "default_ttl": self._default_ttl,
                "cleanup_interval": self._cleanup_interval
            }
    
    def _start_cleanup_task(self):
        """Start the background cleanup task."""
        async def cleanup_loop():
            while True:
                try:
                    await asyncio.sleep(self._cleanup_interval)
                    await self._cleanup_expired()
                except asyncio.CancelledError:
                    break
                except Exception as e:
                    self._logger.error("Cleanup task error", error=str(e))
        
        self._cleanup_task = asyncio.create_task(cleanup_loop())
    
    async def _cleanup_expired(self):
        """Clean up expired cache entries."""
        expired_keys = []
        
        with self._lock:
            for key, entry in self._cache.items():
                if entry.is_expired():
                    expired_keys.append(key)
            
            for key in expired_keys:
                del self._cache[key]
            
            if expired_keys:
                self._stats["cleanups"] += 1
                self._logger.debug(
                    "Cleaned up expired entries",
                    count=len(expired_keys),
                    remaining_size=len(self._cache)
                )
    
    async def close(self):
        """Close the cache and cleanup resources."""
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
        
        await self.clear()
        self._logger.info("Memory cache closed")
