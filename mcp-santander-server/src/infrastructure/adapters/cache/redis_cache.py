"""Redis cache adapter."""

import json
import pickle
from typing import Any, Dict, Optional

import aioredis
import structlog

from ....domain.ports.cache_port import CachePort


logger = structlog.get_logger(__name__)


class RedisCache(CachePort):
    """
    Redis cache adapter.
    
    This adapter implements the CachePort interface using Redis
    for distributed caching with persistence and scalability.
    """
    
    def __init__(
        self,
        url: str = "redis://localhost:6379",
        db: int = 0,
        ttl: int = 3600,
        encoding: str = "utf-8"
    ):
        self._url = url
        self._db = db
        self._default_ttl = ttl
        self._encoding = encoding
        self._redis: Optional[aioredis.Redis] = None
        self._logger = logger.bind(cache="redis", url=url, db=db)
    
    async def _get_redis(self) -> aioredis.Redis:
        """Get or create Redis connection."""
        if self._redis is None:
            self._redis = aioredis.from_url(
                self._url,
                db=self._db,
                encoding=self._encoding,
                decode_responses=False  # We'll handle encoding ourselves
            )
        return self._redis
    
    async def get(self, key: str) -> Optional[Any]:
        """
        Get a value from the cache.
        
        Args:
            key: The cache key
            
        Returns:
            The cached value or None if not found
        """
        try:
            redis = await self._get_redis()
            data = await redis.get(key)
            
            if data is None:
                self._logger.debug("Cache miss", key=key)
                return None
            
            # Try to deserialize the data
            try:
                # First try JSON
                value = json.loads(data.decode(self._encoding))
            except (json.JSONDecodeError, UnicodeDecodeError):
                try:
                    # Fall back to pickle
                    value = pickle.loads(data)
                except Exception:
                    # If all else fails, return as string
                    value = data.decode(self._encoding, errors="ignore")
            
            self._logger.debug("Cache hit", key=key)
            return value
            
        except Exception as e:
            self._logger.error("Cache get error", key=key, error=str(e))
            return None
    
    async def set(
        self, 
        key: str, 
        value: Any, 
        ttl: Optional[int] = None
    ) -> bool:
        """
        Set a value in the cache.
        
        Args:
            key: The cache key
            value: The value to cache
            ttl: Time to live in seconds (None for default)
            
        Returns:
            True if successful, False otherwise
        """
        try:
            redis = await self._get_redis()
            effective_ttl = ttl if ttl is not None else self._default_ttl
            
            # Serialize the value
            try:
                # Try JSON first (more readable)
                data = json.dumps(value).encode(self._encoding)
            except (TypeError, ValueError):
                # Fall back to pickle for complex objects
                data = pickle.dumps(value)
            
            # Set with TTL
            await redis.setex(key, effective_ttl, data)
            
            self._logger.debug("Cache set", key=key, ttl=effective_ttl)
            return True
            
        except Exception as e:
            self._logger.error("Cache set error", key=key, error=str(e))
            return False
    
    async def delete(self, key: str) -> bool:
        """
        Delete a value from the cache.
        
        Args:
            key: The cache key
            
        Returns:
            True if the key was deleted, False if not found
        """
        try:
            redis = await self._get_redis()
            result = await redis.delete(key)
            
            deleted = result > 0
            self._logger.debug("Cache delete", key=key, deleted=deleted)
            return deleted
            
        except Exception as e:
            self._logger.error("Cache delete error", key=key, error=str(e))
            return False
    
    async def exists(self, key: str) -> bool:
        """
        Check if a key exists in the cache.
        
        Args:
            key: The cache key
            
        Returns:
            True if the key exists, False otherwise
        """
        try:
            redis = await self._get_redis()
            result = await redis.exists(key)
            return result > 0
            
        except Exception as e:
            self._logger.error("Cache exists error", key=key, error=str(e))
            return False
    
    async def clear(self) -> bool:
        """
        Clear all values from the cache.
        
        Returns:
            True if successful, False otherwise
        """
        try:
            redis = await self._get_redis()
            await redis.flushdb()
            
            self._logger.info("Cache cleared")
            return True
            
        except Exception as e:
            self._logger.error("Cache clear error", error=str(e))
            return False
    
    async def get_ttl(self, key: str) -> Optional[int]:
        """
        Get the time to live for a key.
        
        Args:
            key: The cache key
            
        Returns:
            TTL in seconds or None if key doesn't exist or has no TTL
        """
        try:
            redis = await self._get_redis()
            ttl = await redis.ttl(key)
            
            if ttl == -2:  # Key doesn't exist
                return None
            elif ttl == -1:  # Key exists but has no TTL
                return None
            else:
                return ttl
                
        except Exception as e:
            self._logger.error("Cache get_ttl error", key=key, error=str(e))
            return None
    
    async def set_ttl(self, key: str, ttl: int) -> bool:
        """
        Set the time to live for an existing key.
        
        Args:
            key: The cache key
            ttl: Time to live in seconds
            
        Returns:
            True if successful, False if key doesn't exist
        """
        try:
            redis = await self._get_redis()
            result = await redis.expire(key, ttl)
            
            success = result > 0
            self._logger.debug("Cache set_ttl", key=key, ttl=ttl, success=success)
            return success
            
        except Exception as e:
            self._logger.error("Cache set_ttl error", key=key, error=str(e))
            return False
    
    async def get_stats(self) -> Dict[str, Any]:
        """
        Get cache statistics.
        
        Returns:
            Dictionary with cache statistics
        """
        try:
            redis = await self._get_redis()
            info = await redis.info()
            
            return {
                "type": "redis",
                "url": self._url,
                "db": self._db,
                "default_ttl": self._default_ttl,
                "connected_clients": info.get("connected_clients", 0),
                "used_memory": info.get("used_memory", 0),
                "used_memory_human": info.get("used_memory_human", "0B"),
                "keyspace_hits": info.get("keyspace_hits", 0),
                "keyspace_misses": info.get("keyspace_misses", 0),
                "total_commands_processed": info.get("total_commands_processed", 0),
                "uptime_in_seconds": info.get("uptime_in_seconds", 0)
            }
            
        except Exception as e:
            self._logger.error("Cache get_stats error", error=str(e))
            return {
                "type": "redis",
                "url": self._url,
                "db": self._db,
                "error": str(e)
            }
    
    async def health_check(self) -> bool:
        """
        Check if Redis is healthy and accessible.
        
        Returns:
            True if healthy, False otherwise
        """
        try:
            redis = await self._get_redis()
            await redis.ping()
            return True
        except Exception:
            return False
    
    async def close(self):
        """Close the Redis connection."""
        if self._redis:
            await self._redis.close()
            self._redis = None
            self._logger.info("Redis cache connection closed")
