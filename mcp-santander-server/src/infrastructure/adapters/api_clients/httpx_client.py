"""HTTPX-based API client adapter."""

import asyncio
from typing import Any, AsyncItera<PERSON>, Dict, List, Optional
import time

import httpx
import structlog
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type

from ....domain.entities.api_request import ApiRequest
from ....domain.entities.api_response import ApiResponse
from ....domain.ports.api_client_port import ApiClientPort
from ....domain.value_objects.response_status import ResponseStatus


logger = structlog.get_logger(__name__)


class HttpxApiClient(ApiClientPort):
    """
    HTTPX-based API client adapter.
    
    This adapter implements the ApiClientPort interface using HTTPX
    for making HTTP requests with retry logic and circuit breaker patterns.
    """
    
    def __init__(
        self,
        timeout: int = 30,
        max_retries: int = 3,
        retry_delay: float = 1.0
    ):
        self._timeout = timeout
        self._max_retries = max_retries
        self._retry_delay = retry_delay
        self._client = httpx.AsyncClient(timeout=timeout)
        self._logger = logger.bind(client="httpx_api_client")
    
    async def execute_request(self, request: ApiRequest) -> ApiResponse:
        """
        Execute an API request and return the response.
        
        Args:
            request: The API request to execute
            
        Returns:
            The API response
            
        Raises:
            ApiClientError: If the request fails
        """
        self._logger.info(
            "Executing API request",
            request_id=str(request.id),
            method=str(request.method),
            url=str(request.endpoint)
        )
        
        start_time = time.time()
        
        try:
            # Prepare the request
            url = str(request.endpoint)
            method = str(request.method)
            headers = request.headers
            params = request.query_params
            json_data = request.body if request.body else None
            
            # Make the HTTP request
            response = await self._client.request(
                method=method,
                url=url,
                headers=headers,
                params=params,
                json=json_data,
                timeout=request.timeout
            )
            
            # Calculate response time
            response_time_ms = (time.time() - start_time) * 1000
            
            # Parse response data
            response_data = None
            try:
                if response.content:
                    response_data = response.json()
            except Exception:
                # If JSON parsing fails, store as text
                response_data = {"text": response.text}
            
            # Determine response status
            response_status = ResponseStatus.from_http_status(response.status_code)
            
            # Create API response entity
            api_response = ApiResponse(
                request_id=request.id,
                status_code=response.status_code,
                status=response_status,
                data=response_data,
                headers=dict(response.headers),
                response_time_ms=response_time_ms
            )
            
            self._logger.info(
                "API request completed",
                request_id=str(request.id),
                status_code=response.status_code,
                response_time_ms=response_time_ms,
                success=api_response.is_successful()
            )
            
            return api_response
            
        except httpx.TimeoutException as e:
            response_time_ms = (time.time() - start_time) * 1000
            self._logger.error(
                "API request timeout",
                request_id=str(request.id),
                timeout=request.timeout,
                response_time_ms=response_time_ms,
                error=str(e)
            )
            
            return ApiResponse(
                request_id=request.id,
                status_code=408,
                status=ResponseStatus.TIMEOUT,
                error_message=f"Request timeout after {request.timeout}s",
                response_time_ms=response_time_ms
            )
            
        except httpx.NetworkError as e:
            response_time_ms = (time.time() - start_time) * 1000
            self._logger.error(
                "API request network error",
                request_id=str(request.id),
                response_time_ms=response_time_ms,
                error=str(e)
            )
            
            return ApiResponse(
                request_id=request.id,
                status_code=0,
                status=ResponseStatus.NETWORK_ERROR,
                error_message=f"Network error: {str(e)}",
                response_time_ms=response_time_ms
            )
            
        except Exception as e:
            response_time_ms = (time.time() - start_time) * 1000
            self._logger.error(
                "Unexpected API request error",
                request_id=str(request.id),
                response_time_ms=response_time_ms,
                error=str(e)
            )
            
            return ApiResponse(
                request_id=request.id,
                status_code=0,
                status=ResponseStatus.ERROR,
                error_message=f"Unexpected error: {str(e)}",
                response_time_ms=response_time_ms
            )
    
    async def execute_request_with_retry(
        self, 
        request: ApiRequest,
        max_retries: Optional[int] = None
    ) -> ApiResponse:
        """
        Execute an API request with retry logic.
        
        Args:
            request: The API request to execute
            max_retries: Override the default max retries
            
        Returns:
            The API response
            
        Raises:
            ApiClientError: If the request fails after all retries
        """
        retries = max_retries if max_retries is not None else self._max_retries
        
        @retry(
            stop=stop_after_attempt(retries + 1),
            wait=wait_exponential(multiplier=self._retry_delay, min=1, max=10),
            retry=retry_if_exception_type((httpx.TimeoutException, httpx.NetworkError))
        )
        async def _execute_with_retry():
            response = await self.execute_request(request)
            
            # Check if we should retry based on response
            if response.is_retryable_error():
                self._logger.warning(
                    "Retryable error, will retry",
                    request_id=str(request.id),
                    status_code=response.status_code,
                    error=response.error_message
                )
                # Raise an exception to trigger retry
                raise httpx.RequestError(f"Retryable error: {response.status_code}")
            
            return response
        
        try:
            return await _execute_with_retry()
        except Exception as e:
            self._logger.error(
                "Request failed after all retries",
                request_id=str(request.id),
                max_retries=retries,
                error=str(e)
            )
            # Return the last response or create an error response
            return ApiResponse(
                request_id=request.id,
                status_code=0,
                status=ResponseStatus.ERROR,
                error_message=f"Failed after {retries} retries: {str(e)}",
                response_time_ms=0.0
            )
    
    async def execute_batch_requests(
        self, 
        requests: List[ApiRequest]
    ) -> List[ApiResponse]:
        """
        Execute multiple API requests concurrently.
        
        Args:
            requests: List of API requests to execute
            
        Returns:
            List of API responses in the same order as requests
        """
        self._logger.info(
            "Executing batch requests",
            count=len(requests)
        )
        
        # Execute all requests concurrently
        tasks = [
            self.execute_request_with_retry(request)
            for request in requests
        ]
        
        responses = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Convert exceptions to error responses
        result_responses = []
        for i, response in enumerate(responses):
            if isinstance(response, Exception):
                error_response = ApiResponse(
                    request_id=requests[i].id,
                    status_code=0,
                    status=ResponseStatus.ERROR,
                    error_message=f"Batch execution error: {str(response)}",
                    response_time_ms=0.0
                )
                result_responses.append(error_response)
            else:
                result_responses.append(response)
        
        self._logger.info(
            "Batch requests completed",
            count=len(requests),
            successful=sum(1 for r in result_responses if r.is_successful())
        )
        
        return result_responses
    
    async def stream_request(
        self, 
        request: ApiRequest
    ) -> AsyncIterator[Dict[str, Any]]:
        """
        Execute a streaming API request.
        
        Args:
            request: The API request to execute
            
        Yields:
            Chunks of response data
            
        Raises:
            ApiClientError: If the request fails
        """
        self._logger.info(
            "Executing streaming API request",
            request_id=str(request.id),
            method=str(request.method),
            url=str(request.endpoint)
        )
        
        try:
            url = str(request.endpoint)
            method = str(request.method)
            headers = request.headers
            params = request.query_params
            json_data = request.body if request.body else None
            
            async with self._client.stream(
                method=method,
                url=url,
                headers=headers,
                params=params,
                json=json_data,
                timeout=request.timeout
            ) as response:
                response.raise_for_status()
                
                async for chunk in response.aiter_bytes():
                    if chunk:
                        yield {"chunk": chunk.decode("utf-8", errors="ignore")}
            
            self._logger.info(
                "Streaming request completed",
                request_id=str(request.id)
            )
            
        except Exception as e:
            self._logger.error(
                "Streaming request error",
                request_id=str(request.id),
                error=str(e)
            )
            raise RuntimeError(f"Streaming request failed: {str(e)}") from e
    
    async def health_check(self) -> bool:
        """
        Check if the API client is healthy and can make requests.
        
        Returns:
            True if healthy, False otherwise
        """
        try:
            # Simple health check - just verify the client is responsive
            return self._client is not None
        except Exception:
            return False
    
    async def get_client_info(self) -> Dict[str, Any]:
        """
        Get information about the API client.
        
        Returns:
            Dictionary with client information
        """
        return {
            "client_type": "httpx",
            "timeout": self._timeout,
            "max_retries": self._max_retries,
            "retry_delay": self._retry_delay,
            "supports_streaming": True,
            "supports_batch": True
        }
    
    async def __aenter__(self):
        """Async context manager entry."""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self._client.aclose()
