"""Application settings and configuration."""

import os
from typing import Any, Dict, List, Optional
from pathlib import Path

from pydantic import BaseSettings, Field, validator
import yaml


class LlmProviderSettings(BaseSettings):
    """Settings for LLM providers."""
    
    enabled: bool = Field(default=True)
    base_url: str = Field(...)
    model: str = Field(...)
    timeout: int = Field(default=60)
    max_retries: int = Field(default=3)
    api_key: Optional[str] = Field(default=None)
    
    class Config:
        """Pydantic configuration."""
        env_prefix = ""


class LlmSettings(BaseSettings):
    """LLM configuration settings."""
    
    providers: Dict[str, LlmProviderSettings] = Field(default_factory=dict)
    
    class Config:
        """Pydantic configuration."""
        env_prefix = "LLM_"


class ApiClientSettings(BaseSettings):
    """API client configuration settings."""
    
    default_timeout: int = Field(default=30)
    max_retries: int = Field(default=3)
    circuit_breaker: Dict[str, Any] = Field(default_factory=dict)
    
    class Config:
        """Pydantic configuration."""
        env_prefix = "API_CLIENT_"


class CacheSettings(BaseSettings):
    """Cache configuration settings."""
    
    provider: str = Field(default="memory")
    redis: Dict[str, Any] = Field(default_factory=dict)
    
    class Config:
        """Pydantic configuration."""
        env_prefix = "CACHE_"


class MonitoringSettings(BaseSettings):
    """Monitoring configuration settings."""
    
    metrics: Dict[str, Any] = Field(default_factory=dict)
    logging: Dict[str, Any] = Field(default_factory=dict)
    
    class Config:
        """Pydantic configuration."""
        env_prefix = "MONITORING_"


class DatabaseSettings(BaseSettings):
    """Database configuration settings."""
    
    mongodb: Dict[str, Any] = Field(default_factory=dict)
    
    class Config:
        """Pydantic configuration."""
        env_prefix = "DATABASE_"


class ServerSettings(BaseSettings):
    """Server configuration settings."""
    
    name: str = Field(default="MCP Santander Server")
    version: str = Field(default="0.1.0")
    debug: bool = Field(default=False)
    log_level: str = Field(default="INFO")
    
    class Config:
        """Pydantic configuration."""
        env_prefix = "SERVER_"


class Settings(BaseSettings):
    """Main application settings."""
    
    environment: str = Field(default="development")
    server: ServerSettings = Field(default_factory=ServerSettings)
    llm: LlmSettings = Field(default_factory=LlmSettings)
    api_clients: ApiClientSettings = Field(default_factory=ApiClientSettings)
    cache: CacheSettings = Field(default_factory=CacheSettings)
    monitoring: MonitoringSettings = Field(default_factory=MonitoringSettings)
    database: DatabaseSettings = Field(default_factory=DatabaseSettings)
    
    class Config:
        """Pydantic configuration."""
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
    
    @classmethod
    def load_from_yaml(cls, config_path: Optional[str] = None) -> "Settings":
        """
        Load settings from YAML configuration file.
        
        Args:
            config_path: Path to configuration file
            
        Returns:
            Settings instance
        """
        if config_path is None:
            environment = os.getenv("ENVIRONMENT", "development")
            config_path = f"config/{environment}.yaml"
        
        config_file = Path(config_path)
        if not config_file.exists():
            # Return default settings if config file doesn't exist
            return cls()
        
        with open(config_file, "r", encoding="utf-8") as f:
            config_data = yaml.safe_load(f)
        
        # Create settings from YAML data
        return cls.parse_obj(config_data)
    
    def get_llm_provider_settings(self, provider: str) -> Optional[LlmProviderSettings]:
        """
        Get settings for a specific LLM provider.
        
        Args:
            provider: Provider name
            
        Returns:
            Provider settings or None if not found
        """
        provider_config = self.llm.providers.get(provider)
        if not provider_config:
            return None
        
        return LlmProviderSettings.parse_obj(provider_config)
    
    def get_enabled_llm_providers(self) -> List[str]:
        """
        Get list of enabled LLM providers.
        
        Returns:
            List of enabled provider names
        """
        enabled_providers = []
        for provider, config in self.llm.providers.items():
            if isinstance(config, dict) and config.get("enabled", True):
                enabled_providers.append(provider)
            elif hasattr(config, "enabled") and config.enabled:
                enabled_providers.append(provider)
        
        return enabled_providers
    
    def is_development(self) -> bool:
        """Check if running in development environment."""
        return self.environment.lower() == "development"
    
    def is_production(self) -> bool:
        """Check if running in production environment."""
        return self.environment.lower() == "production"


# Global settings instance
_settings: Optional[Settings] = None


def get_settings() -> Settings:
    """
    Get the global settings instance.
    
    Returns:
        Settings instance
    """
    global _settings
    if _settings is None:
        _settings = Settings.load_from_yaml()
    return _settings


def reload_settings(config_path: Optional[str] = None) -> Settings:
    """
    Reload settings from configuration file.
    
    Args:
        config_path: Path to configuration file
        
    Returns:
        New settings instance
    """
    global _settings
    _settings = Settings.load_from_yaml(config_path)
    return _settings
