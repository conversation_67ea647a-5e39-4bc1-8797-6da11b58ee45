"""Dependency injection container configuration."""

from dependency_injector import containers, providers
from dependency_injector.wiring import Provide, inject

from .settings import Settings, get_settings
from ..adapters.llm.ollama_client import OllamaClient
from ..adapters.llm.openai_client import OpenAIClient
from ..adapters.llm.llm_client_factory import LlmClientFactory
from ..adapters.api_clients.httpx_client import HttpxApiClient
from ..adapters.cache.memory_cache import MemoryCache
from ..adapters.cache.redis_cache import RedisCache
from ..adapters.monitoring.prometheus_metrics import PrometheusMetrics
from ...domain.services.api_orchestrator import ApiOrchestratorService
from ...application.services.api_service import ApiService
from ...application.services.llm_service import LlmService
from ...application.use_cases.execute_api_request import ExecuteApiRequestUseCase
from ...application.use_cases.analyze_api_response import AnalyzeApiResponseUseCase
from ...application.use_cases.suggest_api_optimization import SuggestApiOptimizationUseCase
from ...application.use_cases.generate_llm_completion import GenerateLlmCompletionUseCase


class Container(containers.DeclarativeContainer):
    """Dependency injection container."""
    
    # Configuration
    config = providers.Configuration()
    settings = providers.Singleton(get_settings)
    
    # Infrastructure - Cache
    memory_cache = providers.Singleton(MemoryCache)
    
    redis_cache = providers.Singleton(
        RedisCache,
        url=config.cache.redis.url,
        db=config.cache.redis.db,
        ttl=config.cache.redis.ttl
    )
    
    cache = providers.Selector(
        config.cache.provider,
        memory=memory_cache,
        redis=redis_cache
    )
    
    # Infrastructure - Monitoring
    metrics = providers.Singleton(
        PrometheusMetrics,
        port=config.monitoring.metrics.port,
        enabled=config.monitoring.metrics.enabled
    )
    
    # Infrastructure - LLM Clients
    ollama_client = providers.Singleton(
        OllamaClient,
        base_url=config.llm.providers.ollama.base_url,
        model=config.llm.providers.ollama.model,
        timeout=config.llm.providers.ollama.timeout
    )
    
    openai_client = providers.Singleton(
        OpenAIClient,
        api_key=config.llm.providers.openai.api_key,
        model=config.llm.providers.openai.model,
        timeout=config.llm.providers.openai.timeout
    )
    
    llm_client_factory = providers.Singleton(
        LlmClientFactory,
        ollama_client=ollama_client,
        openai_client=openai_client,
        settings=settings
    )
    
    # Get the primary LLM client (Ollama by default)
    llm_client = providers.Factory(
        llm_client_factory.provided.get_client,
        provider="ollama"
    )
    
    # Infrastructure - API Client
    api_client = providers.Singleton(
        HttpxApiClient,
        timeout=config.api_clients.default_timeout,
        max_retries=config.api_clients.max_retries
    )
    
    # Infrastructure - Repository (placeholder for now)
    # TODO: Implement actual repository when database is set up
    repository = providers.Object(None)
    
    # Domain Services
    api_orchestrator = providers.Singleton(
        ApiOrchestratorService,
        api_client=api_client,
        llm_client=llm_client,
        cache=cache,
        repository=repository
    )
    
    # Use Cases
    execute_api_request_use_case = providers.Factory(
        ExecuteApiRequestUseCase,
        api_orchestrator=api_orchestrator
    )
    
    analyze_api_response_use_case = providers.Factory(
        AnalyzeApiResponseUseCase,
        api_orchestrator=api_orchestrator,
        repository=repository
    )
    
    suggest_api_optimization_use_case = providers.Factory(
        SuggestApiOptimizationUseCase,
        api_orchestrator=api_orchestrator,
        repository=repository
    )
    
    generate_llm_completion_use_case = providers.Factory(
        GenerateLlmCompletionUseCase,
        llm_client=llm_client,
        repository=repository
    )
    
    # Application Services
    api_service = providers.Factory(
        ApiService,
        execute_api_request_use_case=execute_api_request_use_case,
        analyze_api_response_use_case=analyze_api_response_use_case,
        suggest_api_optimization_use_case=suggest_api_optimization_use_case,
        repository=repository
    )
    
    llm_service = providers.Factory(
        LlmService,
        generate_llm_completion_use_case=generate_llm_completion_use_case,
        llm_client=llm_client,
        repository=repository
    )


def create_container() -> Container:
    """
    Create and configure the dependency injection container.
    
    Returns:
        Configured container instance
    """
    container = Container()
    
    # Load settings
    settings = get_settings()
    
    # Configure the container with settings
    container.config.from_dict({
        "cache": {
            "provider": settings.cache.provider,
            "redis": settings.cache.redis
        },
        "monitoring": {
            "metrics": settings.monitoring.metrics
        },
        "llm": {
            "providers": settings.llm.providers
        },
        "api_clients": {
            "default_timeout": settings.api_clients.default_timeout,
            "max_retries": settings.api_clients.max_retries
        }
    })
    
    return container


# Global container instance
_container: Container = None


def get_container() -> Container:
    """
    Get the global container instance.
    
    Returns:
        Container instance
    """
    global _container
    if _container is None:
        _container = create_container()
    return _container
