# MCP Santander Server

Scalable Model Context Protocol (MCP) Server with Hexagonal Architecture for Internal API Queries.

## Features

- **Hexagonal Architecture**: Clean separation of concerns with Domain, Application, Infrastructure, and Presentation layers
- **Multi-LLM Support**: Local (Ollama) and remote (OpenAI) LLM providers
- **Scalable Design**: Circuit breakers, caching, monitoring, and resilience patterns
- **Extensible**: Plugin architecture for easy addition of new API clients and features
- **Production Ready**: Comprehensive logging, metrics, and configuration management

## Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    Presentation Layer                       │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │ MCP Tools   │  │ MCP Resources│  │ MCP Prompts │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                   Application Layer                         │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │ Use Cases   │  │ App Services│  │    DTOs     │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                     Domain Layer                            │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │  Entities   │  │Value Objects│  │   Ports     │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                 Infrastructure Layer                        │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │LLM Adapters │  │API Clients  │  │   Cache     │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
└─────────────────────────────────────────────────────────────┘
```

## Quick Start

### Prerequisites

- Python 3.13+
- uv (recommended) or pip
- Ollama (for local LLM)
- Redis (optional, for caching)
- MongoDB (optional, for persistence)

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd mcp-santander-server
```

2. Install dependencies:
```bash
uv sync
# or
pip install -e .
```

3. Set up environment:
```bash
cp .env.example .env
# Edit .env with your configuration
```

4. Start Ollama (for local LLM):
```bash
ollama serve
ollama pull llama3.2:latest
```

### Running the Server

Development mode:
```bash
uv run python main.py
# or
mcp dev main.py
```

Production mode:
```bash
ENVIRONMENT=production uv run python main.py
```

### Testing

Run all tests:
```bash
uv run pytest
```

Run specific test types:
```bash
# Unit tests
uv run pytest tests/unit/

# Integration tests
uv run pytest tests/integration/

# End-to-end tests
uv run pytest tests/e2e/
```

## Configuration

The server supports multiple configuration methods:

1. **YAML files**: `config/development.yaml`, `config/production.yaml`
2. **Environment variables**: See `.env.example`
3. **Runtime configuration**: Via dependency injection container

## LLM Providers

### Ollama (Local)
- Default model: `llama3.2:latest`
- Base URL: `http://localhost:11434`
- Supports streaming and async operations

### OpenAI (Remote)
- Requires API key
- Default model: `gpt-4`
- Full API compatibility

## Development

### Project Structure

```
src/
├── domain/           # Core business logic
│   ├── entities/     # Domain entities
│   ├── value_objects/# Value objects
│   ├── services/     # Domain services
│   └── ports/        # Interfaces/contracts
├── application/      # Application logic
│   ├── use_cases/    # Use case implementations
│   ├── services/     # Application services
│   └── dto/          # Data transfer objects
├── infrastructure/   # External concerns
│   ├── adapters/     # External service adapters
│   └── config/       # Configuration management
└── presentation/     # MCP interface
    └── mcp/          # MCP tools, resources, prompts
```

### Adding New Features

1. Create a new branch: `git checkout -b feature/new-feature`
2. Implement in the appropriate layer
3. Add comprehensive tests
4. Update documentation
5. Create pull request

## Contributing

1. Follow the hexagonal architecture principles
2. Write tests for all new functionality
3. Use type hints and proper documentation
4. Follow the existing code style (black, isort, flake8)

## License

[Add your license here]