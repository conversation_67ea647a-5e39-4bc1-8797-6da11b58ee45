"""Unit tests for memory cache adapter."""

import asyncio
import pytest

from src.infrastructure.adapters.cache.memory_cache import MemoryCache


class TestMemoryCache:
    """Test cases for MemoryCache adapter."""
    
    @pytest.fixture
    async def cache(self):
        """Create a memory cache instance for testing."""
        cache = MemoryCache(default_ttl=60, cleanup_interval=1)
        yield cache
        await cache.close()
    
    async def test_set_and_get(self, cache):
        """Test basic set and get operations."""
        # Set a value
        result = await cache.set("test_key", "test_value")
        assert result is True
        
        # Get the value
        value = await cache.get("test_key")
        assert value == "test_value"
    
    async def test_get_nonexistent_key(self, cache):
        """Test getting a non-existent key."""
        value = await cache.get("nonexistent_key")
        assert value is None
    
    async def test_delete(self, cache):
        """Test deleting a key."""
        # Set a value
        await cache.set("test_key", "test_value")
        
        # Delete the key
        result = await cache.delete("test_key")
        assert result is True
        
        # Verify it's gone
        value = await cache.get("test_key")
        assert value is None
        
        # Try to delete again
        result = await cache.delete("test_key")
        assert result is False
    
    async def test_exists(self, cache):
        """Test checking if a key exists."""
        # Key doesn't exist initially
        exists = await cache.exists("test_key")
        assert exists is False
        
        # Set a value
        await cache.set("test_key", "test_value")
        
        # Key should exist now
        exists = await cache.exists("test_key")
        assert exists is True
        
        # Delete the key
        await cache.delete("test_key")
        
        # Key shouldn't exist anymore
        exists = await cache.exists("test_key")
        assert exists is False
    
    async def test_clear(self, cache):
        """Test clearing all cache entries."""
        # Set multiple values
        await cache.set("key1", "value1")
        await cache.set("key2", "value2")
        await cache.set("key3", "value3")
        
        # Clear the cache
        result = await cache.clear()
        assert result is True
        
        # Verify all keys are gone
        assert await cache.get("key1") is None
        assert await cache.get("key2") is None
        assert await cache.get("key3") is None
    
    async def test_ttl_functionality(self, cache):
        """Test TTL (time to live) functionality."""
        # Set a value with short TTL
        await cache.set("test_key", "test_value", ttl=1)
        
        # Value should exist immediately
        value = await cache.get("test_key")
        assert value == "test_value"
        
        # Check TTL
        ttl = await cache.get_ttl("test_key")
        assert ttl is not None
        assert 0 <= ttl <= 1
        
        # Wait for expiration
        await asyncio.sleep(1.1)
        
        # Value should be expired
        value = await cache.get("test_key")
        assert value is None
    
    async def test_set_ttl(self, cache):
        """Test setting TTL for existing key."""
        # Set a value with default TTL
        await cache.set("test_key", "test_value")
        
        # Update TTL
        result = await cache.set_ttl("test_key", 2)
        assert result is True
        
        # Check new TTL
        ttl = await cache.get_ttl("test_key")
        assert ttl is not None
        assert 1 <= ttl <= 2
        
        # Try to set TTL for non-existent key
        result = await cache.set_ttl("nonexistent_key", 10)
        assert result is False
    
    async def test_complex_data_types(self, cache):
        """Test storing complex data types."""
        # Dictionary
        dict_data = {"key": "value", "number": 42, "list": [1, 2, 3]}
        await cache.set("dict_key", dict_data)
        retrieved_dict = await cache.get("dict_key")
        assert retrieved_dict == dict_data
        
        # List
        list_data = [1, "two", {"three": 3}]
        await cache.set("list_key", list_data)
        retrieved_list = await cache.get("list_key")
        assert retrieved_list == list_data
        
        # None value
        await cache.set("none_key", None)
        retrieved_none = await cache.get("none_key")
        assert retrieved_none is None
    
    async def test_stats(self, cache):
        """Test cache statistics."""
        # Initial stats
        stats = await cache.get_stats()
        assert stats["type"] == "memory"
        assert stats["size"] == 0
        assert stats["hits"] == 0
        assert stats["misses"] == 0
        
        # Perform some operations
        await cache.set("key1", "value1")  # +1 set
        await cache.get("key1")  # +1 hit
        await cache.get("nonexistent")  # +1 miss
        await cache.delete("key1")  # +1 delete
        
        # Check updated stats
        stats = await cache.get_stats()
        assert stats["sets"] >= 1
        assert stats["hits"] >= 1
        assert stats["misses"] >= 1
        assert stats["deletes"] >= 1
        assert stats["hit_rate"] > 0
    
    async def test_expired_key_cleanup(self, cache):
        """Test that expired keys are properly cleaned up."""
        # Set a key with very short TTL
        await cache.set("short_lived", "value", ttl=1)
        
        # Verify it exists
        assert await cache.exists("short_lived") is True
        
        # Wait for expiration
        await asyncio.sleep(1.1)
        
        # Accessing expired key should remove it
        value = await cache.get("short_lived")
        assert value is None
        
        # Key should no longer exist
        assert await cache.exists("short_lived") is False
    
    async def test_concurrent_access(self, cache):
        """Test concurrent access to the cache."""
        async def set_values(start_idx, count):
            for i in range(start_idx, start_idx + count):
                await cache.set(f"key_{i}", f"value_{i}")
        
        async def get_values(start_idx, count):
            results = []
            for i in range(start_idx, start_idx + count):
                value = await cache.get(f"key_{i}")
                results.append(value)
            return results
        
        # Set values concurrently
        await asyncio.gather(
            set_values(0, 10),
            set_values(10, 10),
            set_values(20, 10)
        )
        
        # Get values concurrently
        results = await asyncio.gather(
            get_values(0, 10),
            get_values(10, 10),
            get_values(20, 10)
        )
        
        # Verify all values were set correctly
        all_results = [item for sublist in results for item in sublist]
        expected_values = [f"value_{i}" for i in range(30)]
        assert all_results == expected_values
