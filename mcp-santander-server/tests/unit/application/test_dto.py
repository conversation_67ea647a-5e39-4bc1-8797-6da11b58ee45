"""Unit tests for application DTOs."""

import pytest
from uuid import uuid4

from src.application.dto.api_request_dto import (
    CreateApiRequestDto,
    ExecuteApiRequestDto,
    ApiRequestListDto
)
from src.application.dto.llm_request_dto import (
    LlmMessageDto,
    CreateLlmRequestDto,
    LlmCompletionDto,
    AnalyzeApiResponseDto,
    OptimizationSuggestionDto
)


class TestApiRequestDtos:
    """Test cases for API request DTOs."""
    
    def test_create_api_request_dto_valid(self):
        """Test creating a valid CreateApiRequestDto."""
        dto = CreateApiRequestDto(
            base_url="https://api.example.com",
            path="/users",
            method="GET",
            headers={"Authorization": "Bearer token"},
            query_params={"limit": 10},
            timeout=30,
            max_retries=3
        )
        
        assert dto.base_url == "https://api.example.com"
        assert dto.path == "/users"
        assert dto.method == "GET"
        assert dto.headers == {"Authorization": "Bearer token"}
        assert dto.query_params == {"limit": 10}
        assert dto.timeout == 30
        assert dto.max_retries == 3
    
    def test_create_api_request_dto_method_validation(self):
        """Test method validation in CreateApiRequestDto."""
        # Valid method (case insensitive)
        dto = CreateApiRequestDto(
            base_url="https://api.example.com",
            path="/users",
            method="get"
        )
        assert dto.method == "GET"
        
        # Invalid method
        with pytest.raises(ValueError, match="Invalid HTTP method"):
            CreateApiRequestDto(
                base_url="https://api.example.com",
                path="/users",
                method="INVALID"
            )
    
    def test_create_api_request_dto_timeout_validation(self):
        """Test timeout validation in CreateApiRequestDto."""
        with pytest.raises(ValueError, match="Timeout must be positive"):
            CreateApiRequestDto(
                base_url="https://api.example.com",
                path="/users",
                method="GET",
                timeout=0
            )
    
    def test_create_api_request_dto_max_retries_validation(self):
        """Test max retries validation in CreateApiRequestDto."""
        with pytest.raises(ValueError, match="Max retries cannot be negative"):
            CreateApiRequestDto(
                base_url="https://api.example.com",
                path="/users",
                method="GET",
                max_retries=-1
            )
    
    def test_execute_api_request_dto_valid(self):
        """Test creating a valid ExecuteApiRequestDto."""
        dto = ExecuteApiRequestDto(
            base_url="https://api.example.com",
            path="/users",
            method="POST",
            body={"name": "John"},
            use_cache=False,
            llm_context="User creation request"
        )
        
        assert dto.base_url == "https://api.example.com"
        assert dto.path == "/users"
        assert dto.method == "POST"
        assert dto.body == {"name": "John"}
        assert dto.use_cache is False
        assert dto.llm_context == "User creation request"
    
    def test_api_request_list_dto_valid(self):
        """Test creating a valid ApiRequestListDto."""
        dto = ApiRequestListDto(
            limit=50,
            offset=10,
            status_filter="completed",
            method_filter="GET"
        )
        
        assert dto.limit == 50
        assert dto.offset == 10
        assert dto.status_filter == "completed"
        assert dto.method_filter == "GET"
    
    def test_api_request_list_dto_limit_validation(self):
        """Test limit validation in ApiRequestListDto."""
        # Too small
        with pytest.raises(ValueError, match="Limit must be between 1 and 1000"):
            ApiRequestListDto(limit=0)
        
        # Too large
        with pytest.raises(ValueError, match="Limit must be between 1 and 1000"):
            ApiRequestListDto(limit=1001)
    
    def test_api_request_list_dto_offset_validation(self):
        """Test offset validation in ApiRequestListDto."""
        with pytest.raises(ValueError, match="Offset cannot be negative"):
            ApiRequestListDto(offset=-1)


class TestLlmRequestDtos:
    """Test cases for LLM request DTOs."""
    
    def test_llm_message_dto_valid(self):
        """Test creating a valid LlmMessageDto."""
        dto = LlmMessageDto(
            role="user",
            content="Hello, world!",
            metadata={"timestamp": "2024-01-01"}
        )
        
        assert dto.role == "user"
        assert dto.content == "Hello, world!"
        assert dto.metadata == {"timestamp": "2024-01-01"}
    
    def test_llm_message_dto_role_validation(self):
        """Test role validation in LlmMessageDto."""
        # Valid role (case insensitive)
        dto = LlmMessageDto(role="USER", content="Hello")
        assert dto.role == "user"
        
        # Invalid role
        with pytest.raises(ValueError, match="Invalid role"):
            LlmMessageDto(role="invalid", content="Hello")
    
    def test_create_llm_request_dto_valid(self):
        """Test creating a valid CreateLlmRequestDto."""
        messages = [
            LlmMessageDto(role="system", content="You are helpful."),
            LlmMessageDto(role="user", content="Hello!")
        ]
        
        dto = CreateLlmRequestDto(
            provider="ollama",
            model="llama3.2:latest",
            messages=messages,
            temperature=0.8,
            max_tokens=1000,
            stream=True,
            timeout=60
        )
        
        assert dto.provider == "ollama"
        assert dto.model == "llama3.2:latest"
        assert len(dto.messages) == 2
        assert dto.temperature == 0.8
        assert dto.max_tokens == 1000
        assert dto.stream is True
        assert dto.timeout == 60
    
    def test_create_llm_request_dto_provider_validation(self):
        """Test provider validation in CreateLlmRequestDto."""
        messages = [LlmMessageDto(role="user", content="Hello")]
        
        # Valid provider (case insensitive)
        dto = CreateLlmRequestDto(
            provider="OLLAMA",
            model="llama3.2:latest",
            messages=messages
        )
        assert dto.provider == "ollama"
        
        # Invalid provider
        with pytest.raises(ValueError, match="Invalid provider"):
            CreateLlmRequestDto(
                provider="invalid",
                model="model",
                messages=messages
            )
    
    def test_create_llm_request_dto_temperature_validation(self):
        """Test temperature validation in CreateLlmRequestDto."""
        messages = [LlmMessageDto(role="user", content="Hello")]
        
        # Too low
        with pytest.raises(ValueError, match="Temperature must be between 0.0 and 2.0"):
            CreateLlmRequestDto(
                provider="ollama",
                model="llama3.2:latest",
                messages=messages,
                temperature=-0.1
            )
        
        # Too high
        with pytest.raises(ValueError, match="Temperature must be between 0.0 and 2.0"):
            CreateLlmRequestDto(
                provider="ollama",
                model="llama3.2:latest",
                messages=messages,
                temperature=2.1
            )
    
    def test_create_llm_request_dto_max_tokens_validation(self):
        """Test max tokens validation in CreateLlmRequestDto."""
        messages = [LlmMessageDto(role="user", content="Hello")]
        
        with pytest.raises(ValueError, match="Max tokens must be positive"):
            CreateLlmRequestDto(
                provider="ollama",
                model="llama3.2:latest",
                messages=messages,
                max_tokens=0
            )
    
    def test_create_llm_request_dto_messages_validation(self):
        """Test messages validation in CreateLlmRequestDto."""
        with pytest.raises(ValueError, match="At least one message is required"):
            CreateLlmRequestDto(
                provider="ollama",
                model="llama3.2:latest",
                messages=[]
            )
    
    def test_analyze_api_response_dto_valid(self):
        """Test creating a valid AnalyzeApiResponseDto."""
        response_id = uuid4()
        
        dto = AnalyzeApiResponseDto(
            response_id=response_id,
            analysis_prompt="Analyze this response",
            provider="openai",
            model="gpt-4"
        )
        
        assert dto.response_id == response_id
        assert dto.analysis_prompt == "Analyze this response"
        assert dto.provider == "openai"
        assert dto.model == "gpt-4"
    
    def test_optimization_suggestion_dto_valid(self):
        """Test creating a valid OptimizationSuggestionDto."""
        request_id = uuid4()
        response_id = uuid4()
        
        dto = OptimizationSuggestionDto(
            request_id=request_id,
            response_id=response_id,
            provider="ollama",
            model="llama3.2:latest"
        )
        
        assert dto.request_id == request_id
        assert dto.response_id == response_id
        assert dto.provider == "ollama"
        assert dto.model == "llama3.2:latest"
