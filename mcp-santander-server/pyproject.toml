[project]
name = "mcp-santander-server"
version = "0.1.0"
description = "Scalable MCP Server with Hexagonal Architecture for Internal API Queries"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    # Core MCP
    "mcp[cli]>=1.9.1",

    # Dependency Injection & Configuration
    "dependency-injector>=4.46.0",
    "pydantic>=2.11.5",
    "pydantic-settings>=2.9.1",
    "pyyaml>=6.0.1",

    # HTTP Clients & API
    "httpx>=0.28.1",
    "aiohttp>=3.9.0",

    # LLM Support
    "openai>=1.54.0",
    "ollama>=0.3.3",

    # Database & Cache
    "motor>=3.7.1",
    "redis>=5.1.1",
    "aioredis>=2.0.1",

    # Monitoring & Logging
    "prometheus-client>=0.22.0",
    "structlog>=25.3.0",

    # Circuit Breaker & Resilience
    "tenacity>=9.0.0",
    "circuitbreaker>=2.0.0",

    # Testing & Development
    "pytest>=8.0.0",
    "pytest-asyncio>=0.24.0",
    "pytest-cov>=6.0.0",
    "pytest-mock>=3.14.0",
    "factory-boy>=3.3.1",
    "faker>=30.8.2",
]

[project.optional-dependencies]
dev = [
    "black>=24.0.0",
    "isort>=5.13.0",
    "flake8>=7.0.0",
    "mypy>=1.13.0",
    "pre-commit>=4.0.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--cov=src",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
]
asyncio_mode = "auto"

[tool.black]
line-length = 88
target-version = ['py313']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["src"]

[tool.mypy]
python_version = "3.13"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
