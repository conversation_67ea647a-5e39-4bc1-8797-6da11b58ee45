# Production Configuration
server:
  name: "MCP Santander Server"
  version: "0.1.0"
  debug: false
  log_level: "INFO"

llm:
  providers:
    ollama:
      enabled: true
      base_url: "${OLLAMA_BASE_URL:-http://localhost:11434}"
      model: "${OLLAMA_MODEL:-llama3.2:latest}"
      timeout: 60
      max_retries: 5
    openai:
      enabled: true
      api_key: "${OPENAI_API_KEY}"
      model: "${OPENAI_MODEL:-gpt-4}"
      timeout: 60
      max_retries: 5

api_clients:
  default_timeout: 60
  max_retries: 5
  circuit_breaker:
    failure_threshold: 10
    recovery_timeout: 120
    expected_exception: "httpx.RequestError"

cache:
  provider: "redis"
  redis:
    url: "${REDIS_URL:-redis://localhost:6379}"
    db: "${REDIS_DB:-0}"
    ttl: 7200

monitoring:
  metrics:
    enabled: true
    port: 8080
    path: "/metrics"
  logging:
    level: "INFO"
    format: "json"
    structured: true

database:
  mongodb:
    uri: "${MONGODB_URI:-mongodb://localhost:27017}"
    database: "${MONGODB_DATABASE:-mcp_santander}"
    collection_prefix: ""
