# Development Configuration
server:
  name: "MCP Santander Server - Dev"
  version: "0.1.0"
  debug: true
  log_level: "DEBUG"

llm:
  providers:
    ollama:
      enabled: true
      base_url: "http://localhost:11434"
      model: "llama3.2:latest"
      timeout: 30
      max_retries: 3
    openai:
      enabled: false
      api_key: "${OPENAI_API_KEY}"
      model: "gpt-4"
      timeout: 30
      max_retries: 3

api_clients:
  default_timeout: 30
  max_retries: 3
  circuit_breaker:
    failure_threshold: 5
    recovery_timeout: 60
    expected_exception: "httpx.RequestError"

cache:
  provider: "memory"  # memory, redis
  redis:
    url: "redis://localhost:6379"
    db: 0
    ttl: 3600

monitoring:
  metrics:
    enabled: true
    port: 8080
    path: "/metrics"
  logging:
    level: "DEBUG"
    format: "json"
    structured: true

database:
  mongodb:
    uri: "mongodb://localhost:27017"
    database: "mcp_santander_dev"
    collection_prefix: "dev_"
